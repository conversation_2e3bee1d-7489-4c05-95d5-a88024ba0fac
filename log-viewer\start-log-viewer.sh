#!/bin/bash

# TTS代理日志查看器启动脚本

echo "🔍 启动TTS代理日志查看器..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误：请在log-viewer目录中运行此脚本"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误：未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查PM2
if ! command -v pm2 &> /dev/null; then
    echo "❌ 错误：未找到PM2，请先安装PM2: npm install -g pm2"
    exit 1
fi

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 创建日志目录
mkdir -p logs

# 设置环境变量（如果未设置）
if [ -z "$LOG_PASSWORD" ]; then
    export LOG_PASSWORD="logviewer123"
    echo "🔐 使用默认密码: logviewer123"
    echo "   建议设置环境变量: export LOG_PASSWORD=\"your_secure_password\""
fi

if [ -z "$LOG_VIEWER_PORT" ]; then
    export LOG_VIEWER_PORT=3001
fi

if [ -z "$LOG_PATH" ]; then
    export LOG_PATH="/var/www/tts-proxy-vps/logs"
fi

# 检查PM2日志文件是否存在
if [ ! -f "$LOG_PATH/out.0.log" ]; then
    echo "⚠️  警告：未找到 $LOG_PATH/out.0.log"
    echo "   请确保TTS代理服务已启动并生成PM2日志文件"
fi

if [ ! -f "$LOG_PATH/error.0.log" ]; then
    echo "⚠️  警告：未找到 $LOG_PATH/error.0.log"
    echo "   请确保TTS代理服务已启动并生成PM2日志文件"
fi

# 停止已存在的实例
echo "🔄 检查现有实例..."
pm2 stop log-viewer 2>/dev/null || true
pm2 delete log-viewer 2>/dev/null || true

# 启动服务
echo "🚀 启动日志查看器..."
pm2 start ecosystem.config.cjs

# 显示状态
echo ""
echo "✅ 日志查看器已启动！"
echo ""
echo "📊 服务状态："
pm2 status log-viewer

echo ""
echo "🌐 访问地址："
echo "   本地访问: http://localhost:$LOG_VIEWER_PORT"
echo "   认证密码: $LOG_PASSWORD"
echo ""
echo "📋 常用命令："
echo "   查看状态: pm2 status log-viewer"
echo "   查看日志: pm2 logs log-viewer"
echo "   重启服务: pm2 restart log-viewer"
echo "   停止服务: pm2 stop log-viewer"
echo ""
echo "🔍 监控的PM2日志文件："
echo "   输出日志: $(realpath $LOG_PATH/out.0.log 2>/dev/null || echo "$LOG_PATH/out.0.log")"
echo "   错误日志: $(realpath $LOG_PATH/error.0.log 2>/dev/null || echo "$LOG_PATH/error.0.log")"
echo ""

# 检查端口是否可访问
sleep 2
if curl -s http://localhost:$LOG_VIEWER_PORT/health > /dev/null; then
    echo "✅ 服务健康检查通过"
else
    echo "❌ 服务健康检查失败，请检查日志: pm2 logs log-viewer"
fi

echo ""
echo "🎉 部署完成！现在可以通过浏览器访问日志查看器了。"
