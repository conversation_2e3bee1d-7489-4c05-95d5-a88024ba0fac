# 🔧 环境变量配置示例
# 复制此文件为 .env 并填入您的实际配置

# 【代理服务配置】IP代理设置
# 请将以下占位符替换为您的实际代理信息
PROXY_IP=你的代理IP或域名
PROXY_PORT=你的代理端口
PROXY_USERNAME=你的用户名
PROXY_PASSWORD=你的密码

# 【动态地区功能】启用动态地区切换（可选）
# 设置为 true 启用动态地区功能，false 或不设置则使用固定代理
ENABLE_DYNAMIC_REGIONS=false

# 【应用配置】
NODE_ENV=production
PORT=3002

# 【安全配置】代理访问密钥
# 建议使用32位以上的随机字符串
PROXY_SECRET=AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9

# 【SSL配置】（通常保持默认值）
# NODE_TLS_REJECT_UNAUTHORIZED=0

# 【示例配置】
# 以下是配置示例，请根据您的实际代理信息修改：
# PROXY_IP=*************
# PROXY_PORT=8080
# PROXY_USERNAME=your_username
# PROXY_PASSWORD=your_password

# 【配置说明】
# 1. PROXY_IP: 您的代理服务器IP地址
# 2. PROXY_PORT: 代理服务器端口号
# 3. PROXY_USERNAME: 代理认证用户名
# 4. PROXY_PASSWORD: 代理认证密码
# 5. PROXY_SECRET: 用于验证客户端请求的密钥
# 6. ENABLE_DYNAMIC_REGIONS: 启用动态地区切换功能

# 【动态地区功能说明】
# 当 ENABLE_DYNAMIC_REGIONS=true 时：
# - 系统会从 lib/regions.json 文件读取地区列表
# - 每次请求随机选择不同的地区
# - 自动构建地区特定的代理认证信息
# - 支持故障转移：如果某个地区失败，自动重试其他地区
# - 格式：PROXY_PASSWORD + "-{CountryCode}_{RegionCode}_city_{CityCode}"
#
# 当 ENABLE_DYNAMIC_REGIONS=false 或未设置时：
# - 使用静态代理配置（全局模式）
# - 所有请求使用相同的代理认证信息
# - 自动在密码后添加"-global"后缀
# - 格式：PROXY_PASSWORD + "-global"

# 【部署提示】
# 生产环境建议通过系统环境变量或PM2配置文件设置这些值
# 避免在代码仓库中提交包含真实密码的.env文件
