// test-retry-mechanisms.js - 重试机制和错误场景单元测试
// 专门测试401、403、quota_exceeded等场景的重试逻辑

import fetch from 'node-fetch';

// 测试配置
const TEST_CONFIG = {
  BASE_URL: 'http://localhost:3007',
  PROXY_SECRET: process.env.PROXY_SECRET || 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9',
  TEST_VOICE_ID: 'test_voice_id',
  TIMEOUT: 5000
};

console.log('🧪 重试机制和错误场景测试');
console.log(`📍 测试服务器: ${TEST_CONFIG.BASE_URL}`);
console.log(`🔑 使用密钥: ${TEST_CONFIG.PROXY_SECRET.substring(0, 4)}...`);

// 模拟ElevenLabs API响应的工具函数
class MockElevenLabsAPI {
  constructor() {
    this.callCount = 0;
    this.responses = [];
  }

  // 设置模拟响应序列
  setResponses(responses) {
    this.responses = responses;
    this.callCount = 0;
  }

  // 获取下一个响应
  getNextResponse() {
    if (this.callCount < this.responses.length) {
      return this.responses[this.callCount++];
    }
    // 如果没有更多响应，返回最后一个
    return this.responses[this.responses.length - 1];
  }

  // 重置计数器
  reset() {
    this.callCount = 0;
  }
}

// 创建模拟API实例
const mockAPI = new MockElevenLabsAPI();

// 测试用例类
class RetryTestCase {
  constructor(name, description, mockResponses, expectedBehavior) {
    this.name = name;
    this.description = description;
    this.mockResponses = mockResponses;
    this.expectedBehavior = expectedBehavior;
  }
}

// 定义测试用例
const TEST_CASES = [
  new RetryTestCase(
    '401 Unauthorized - 不应重试',
    '测试401错误不会触发重试机制',
    [
      { status: 401, body: { error: 'Unauthorized', message: 'Invalid API key' } }
    ],
    { shouldRetry: false, expectedAttempts: 1, finalStatus: 401 }
  ),

  new RetryTestCase(
    '403 Forbidden - 不应重试',
    '测试403错误不会触发重试机制',
    [
      { status: 403, body: { error: 'Forbidden', message: 'Access denied' } }
    ],
    { shouldRetry: false, expectedAttempts: 1, finalStatus: 403 }
  ),

  new RetryTestCase(
    'Quota Exceeded - 不应重试',
    '测试配额超限错误不会触发重试机制',
    [
      { 
        status: 429, 
        body: { 
          status: 'quota_exceeded', 
          message: 'Monthly quota exceeded',
          detail: 'You have exceeded your monthly character limit'
        } 
      }
    ],
    { shouldRetry: false, expectedAttempts: 1, finalStatus: 429 }
  ),

  new RetryTestCase(
    'Network Error - 应该重试',
    '测试网络连接错误会触发重试机制',
    [
      { error: 'ECONNREFUSED' },
      { error: 'ECONNREFUSED' },
      { status: 200, body: { success: true } }
    ],
    { shouldRetry: true, expectedAttempts: 3, finalStatus: 200 }
  ),

  new RetryTestCase(
    'Timeout Error - 应该重试',
    '测试超时错误会触发重试机制',
    [
      { error: 'ETIMEDOUT' },
      { status: 200, body: { success: true } }
    ],
    { shouldRetry: true, expectedAttempts: 2, finalStatus: 200 }
  ),

  new RetryTestCase(
    'Connection Reset - 应该重试',
    '测试连接重置错误会触发重试机制',
    [
      { error: 'ECONNRESET' },
      { error: 'ECONNRESET' },
      { error: 'ECONNRESET' },
      { status: 500, body: { error: 'All retries failed' } }
    ],
    { shouldRetry: true, expectedAttempts: 3, finalStatus: 500 }
  ),

  new RetryTestCase(
    '500 Internal Server Error - 不应重试',
    '测试500错误不会触发重试机制（非网络错误）',
    [
      { status: 500, body: { error: 'Internal Server Error' } }
    ],
    { shouldRetry: false, expectedAttempts: 1, finalStatus: 500 }
  ),

  new RetryTestCase(
    '502 Bad Gateway - 应该重试',
    '测试502错误会触发重试机制（网关错误）',
    [
      { status: 502, body: { error: 'Bad Gateway' } },
      { status: 200, body: { success: true } }
    ],
    { shouldRetry: true, expectedAttempts: 2, finalStatus: 200 }
  )
];

// 电路熔断器测试用例
const CIRCUIT_BREAKER_TESTS = [
  {
    name: '电路熔断器 - 连续失败触发OPEN状态',
    description: '测试连续3次失败后电路熔断器进入OPEN状态',
    failures: 3,
    expectedState: 'OPEN'
  },
  {
    name: '电路熔断器 - HALF_OPEN状态恢复',
    description: '测试OPEN状态30秒后进入HALF_OPEN状态',
    testRecovery: true
  }
];

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

// 工具函数：发送测试请求
async function sendTestRequest(voiceId, requestBody, headers = {}) {
  const url = `${TEST_CONFIG.BASE_URL}/api/v1/text-to-speech/${voiceId}`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': TEST_CONFIG.PROXY_SECRET,
        ...headers
      },
      body: JSON.stringify(requestBody),
      timeout: TEST_CONFIG.TIMEOUT
    });

    let responseBody;
    try {
      responseBody = await response.json();
    } catch {
      responseBody = await response.text();
    }

    return {
      status: response.status,
      statusText: response.statusText,
      body: responseBody,
      headers: Object.fromEntries(response.headers.entries())
    };
  } catch (error) {
    return {
      error: error.message,
      name: error.name
    };
  }
}

// 工具函数：等待指定时间
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 工具函数：记录测试结果
function recordTestResult(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`   ✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`   ❌ ${testName}`);
    if (details) {
      console.log(`      详情: ${details}`);
    }
  }
  
  testResults.details.push({
    name: testName,
    passed,
    details
  });
}

// 测试函数：基本错误响应测试
async function testErrorResponses() {
  console.log('\n🔍 测试错误响应处理...');
  
  for (const testCase of TEST_CASES) {
    console.log(`\n📋 ${testCase.name}`);
    console.log(`   描述: ${testCase.description}`);
    
    // 设置模拟响应
    mockAPI.setResponses(testCase.mockResponses);
    
    const startTime = Date.now();
    const response = await sendTestRequest(
      TEST_CONFIG.TEST_VOICE_ID,
      { text: 'Test message for retry mechanism' }
    );
    const duration = Date.now() - startTime;
    
    console.log(`   响应状态: ${response.status || 'Error'}`);
    console.log(`   响应时间: ${duration}ms`);
    
    // 验证响应状态
    const expectedStatus = testCase.expectedBehavior.finalStatus;
    const statusMatch = response.status === expectedStatus || 
                       (response.error && expectedStatus >= 500);
    
    recordTestResult(
      `状态码验证 (期望: ${expectedStatus})`,
      statusMatch,
      `实际: ${response.status || response.error}`
    );
    
    // 验证重试行为（通过响应时间推断）
    const shouldHaveRetried = testCase.expectedBehavior.shouldRetry;
    const minExpectedDuration = shouldHaveRetried ? 1000 : 0; // 重试至少需要1秒延迟
    const retryBehaviorCorrect = shouldHaveRetried ? 
      duration >= minExpectedDuration : 
      duration < 2000; // 不重试应该很快返回
    
    recordTestResult(
      `重试行为验证`,
      retryBehaviorCorrect,
      `期望重试: ${shouldHaveRetried}, 实际耗时: ${duration}ms`
    );
  }
}

// 简化的电路熔断器测试类（仅用于测试）
class TestCircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 3;
    this.resetTimeout = options.resetTimeout || 5000;
    this.state = 'CLOSED';
    this.failures = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
  }

  async execute(operation) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
        this.successCount = 0;
      } else {
        throw new Error(`Circuit breaker is OPEN - retry in ${Math.ceil((this.resetTimeout - (Date.now() - this.lastFailureTime)) / 1000)} seconds`);
      }
    }

    try {
      const result = await operation();
      if (this.state === 'HALF_OPEN') {
        this.successCount++;
        if (this.successCount >= 2) {
          this.state = 'CLOSED';
          this.failures = 0;
        }
      }
      return result;
    } catch (error) {
      this.failures++;
      this.lastFailureTime = Date.now();
      if (this.failures >= this.failureThreshold && this.state !== 'OPEN') {
        this.state = 'OPEN';
      }
      throw error;
    }
  }

  isHealthy() {
    return this.state === 'CLOSED' || this.state === 'HALF_OPEN';
  }
}

// 测试函数：电路熔断器测试
async function testCircuitBreaker() {
  console.log('\n🔍 测试电路熔断器机制...');

  // 创建测试用的电路熔断器实例
  const testCircuitBreaker = new TestCircuitBreaker({
    failureThreshold: 3,
    resetTimeout: 5000, // 5秒用于测试
    monitoringPeriod: 1000
  });

  console.log('\n📋 测试连续失败触发OPEN状态');

  // 模拟连续失败
  let failureCount = 0;
  for (let i = 0; i < 4; i++) {
    try {
      await testCircuitBreaker.execute(async () => {
        throw new Error('Simulated failure');
      });
    } catch (error) {
      failureCount++;
      console.log(`   失败 ${i + 1}: ${error.message}`);
    }
  }

  // 验证电路熔断器状态
  const isOpen = !testCircuitBreaker.isHealthy();
  recordTestResult(
    '电路熔断器OPEN状态',
    isOpen,
    `状态: ${testCircuitBreaker.state}, 失败次数: ${failureCount}`
  );

  console.log('\n📋 测试OPEN状态下的请求拒绝');

  try {
    await testCircuitBreaker.execute(async () => {
      return 'Should not execute';
    });
    recordTestResult('OPEN状态请求拒绝', false, '请求应该被拒绝');
  } catch (error) {
    const isCorrectError = error.message.includes('Circuit breaker is OPEN');
    recordTestResult(
      'OPEN状态请求拒绝',
      isCorrectError,
      `错误信息: ${error.message}`
    );
  }

  console.log('\n📋 测试电路熔断器恢复机制');
  console.log('   等待5秒进入HALF_OPEN状态...');

  await sleep(5100); // 等待超过resetTimeout

  // 测试HALF_OPEN状态
  try {
    await testCircuitBreaker.execute(async () => {
      return 'Recovery test success';
    });

    const isRecovered = testCircuitBreaker.isHealthy();
    recordTestResult(
      '电路熔断器恢复',
      isRecovered,
      `状态: ${testCircuitBreaker.state}`
    );
  } catch (error) {
    recordTestResult(
      '电路熔断器恢复',
      false,
      `恢复失败: ${error.message}`
    );
  }
}

// 测试函数：并发控制测试
async function testConcurrencyControl() {
  console.log('\n🔍 测试并发控制机制...');

  const concurrentRequests = [];
  const requestCount = 5;

  console.log(`\n📋 发送${requestCount}个并发请求`);

  for (let i = 0; i < requestCount; i++) {
    const request = sendTestRequest(
      TEST_CONFIG.TEST_VOICE_ID,
      { text: `Concurrent test message ${i + 1}` }
    );
    concurrentRequests.push(request);
  }

  const startTime = Date.now();
  const responses = await Promise.allSettled(concurrentRequests);
  const duration = Date.now() - startTime;

  console.log(`   并发请求完成时间: ${duration}ms`);

  // 分析响应
  let successCount = 0;
  let rateLimitedCount = 0;
  let errorCount = 0;

  responses.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const response = result.value;
      if (response.status === 200) {
        successCount++;
      } else if (response.status === 429) {
        rateLimitedCount++;
      } else {
        errorCount++;
      }
      console.log(`   请求${index + 1}: ${response.status || 'Error'}`);
    } else {
      errorCount++;
      console.log(`   请求${index + 1}: 失败 - ${result.reason}`);
    }
  });

  recordTestResult(
    '并发控制响应',
    responses.length === requestCount,
    `成功: ${successCount}, 限流: ${rateLimitedCount}, 错误: ${errorCount}`
  );

  // 验证是否有适当的并发控制
  const hasConcurrencyControl = rateLimitedCount > 0 || successCount < requestCount;
  recordTestResult(
    '并发控制机制',
    hasConcurrencyControl,
    `检测到并发限制或错误处理`
  );
}

// 测试函数：健康检查集成测试
async function testHealthCheckIntegration() {
  console.log('\n🔍 测试健康检查集成...');

  try {
    const response = await fetch(`${TEST_CONFIG.BASE_URL}/api/health`, {
      headers: {
        'x-proxy-secret': TEST_CONFIG.PROXY_SECRET
      }
    });

    const healthData = await response.json();

    console.log(`   健康检查状态: ${response.status}`);
    console.log(`   系统状态: ${healthData.status}`);
    console.log(`   健康评分: ${healthData.healthScore}`);

    // 验证电路熔断器信息
    const hasCircuitBreakerInfo = healthData.circuitBreaker &&
                                 typeof healthData.circuitBreaker.status === 'string';
    recordTestResult(
      '健康检查包含电路熔断器信息',
      hasCircuitBreakerInfo,
      `电路熔断器状态: ${healthData.circuitBreaker?.status}`
    );

    // 验证并发控制信息
    const hasConcurrencyInfo = healthData.concurrency &&
                              typeof healthData.concurrency.status === 'string';
    recordTestResult(
      '健康检查包含并发控制信息',
      hasConcurrencyInfo,
      `并发控制状态: ${healthData.concurrency?.status}`
    );

    // 验证统计信息
    const hasStatistics = healthData.circuitBreaker?.statistics &&
                         typeof healthData.circuitBreaker.statistics.totalRequests === 'number';
    recordTestResult(
      '健康检查包含统计信息',
      hasStatistics,
      `总请求数: ${healthData.circuitBreaker?.statistics?.totalRequests}`
    );

  } catch (error) {
    recordTestResult(
      '健康检查可访问性',
      false,
      `错误: ${error.message}`
    );
  }
}

// 测试函数：特定错误场景模拟
async function testSpecificErrorScenarios() {
  console.log('\n🔍 测试特定错误场景...');

  const errorScenarios = [
    {
      name: 'API密钥无效 (401)',
      headers: { 'x-proxy-secret': 'invalid_secret' },
      expectedStatus: 401,
      shouldRetry: false
    },
    {
      name: '权限不足 (403)',
      body: { text: 'Test with insufficient permissions' },
      expectedStatus: 403,
      shouldRetry: false
    },
    {
      name: '请求体过大',
      body: { text: 'x'.repeat(10000) }, // 超大文本
      expectedStatus: 413,
      shouldRetry: false
    },
    {
      name: '无效的语音ID',
      voiceId: 'invalid_voice_id_12345',
      expectedStatus: 400,
      shouldRetry: false
    }
  ];

  for (const scenario of errorScenarios) {
    console.log(`\n📋 ${scenario.name}`);

    const response = await sendTestRequest(
      scenario.voiceId || TEST_CONFIG.TEST_VOICE_ID,
      scenario.body || { text: 'Test message' },
      scenario.headers || {}
    );

    console.log(`   响应状态: ${response.status || 'Error'}`);

    // 验证状态码
    const statusMatch = response.status === scenario.expectedStatus;
    recordTestResult(
      `${scenario.name} - 状态码`,
      statusMatch,
      `期望: ${scenario.expectedStatus}, 实际: ${response.status}`
    );

    // 验证错误信息格式
    const hasErrorMessage = response.body &&
                           (response.body.error || response.body.message);
    recordTestResult(
      `${scenario.name} - 错误信息`,
      hasErrorMessage,
      `包含错误描述: ${!!hasErrorMessage}`
    );
  }
}

// 测试函数：重试延迟验证
async function testRetryDelayMechanism() {
  console.log('\n🔍 测试重试延迟机制...');

  console.log('\n📋 测试指数退避延迟');

  const delayTests = [
    { attempt: 1, expectedMinDelay: 1000, expectedMaxDelay: 1500 },
    { attempt: 2, expectedMinDelay: 1500, expectedMaxDelay: 2000 },
    { attempt: 3, expectedMinDelay: 2000, expectedMaxDelay: 2500 }
  ];

  for (const test of delayTests) {
    const startTime = Date.now();

    // 模拟重试场景（这里我们通过发送请求到不存在的端点来触发网络错误）
    try {
      await fetch('http://localhost:99999/nonexistent', {
        timeout: 1000
      });
    } catch (error) {
      // 预期的网络错误
    }

    const duration = Date.now() - startTime;

    console.log(`   尝试 ${test.attempt}: 耗时 ${duration}ms`);

    // 注意：这里我们只能测试基本的延迟概念，实际的重试延迟在主代码中实现
    recordTestResult(
      `重试延迟测试 - 尝试 ${test.attempt}`,
      duration >= 500, // 基本的超时检查
      `耗时: ${duration}ms`
    );
  }
}

// 主测试执行函数
async function runAllTests() {
  console.log('🚀 开始执行重试机制和错误场景测试...\n');

  // 检查服务器是否运行
  console.log('🔍 检查服务器状态...');
  try {
    const response = await fetch(`${TEST_CONFIG.BASE_URL}/`, { timeout: 5000 });
    if (!response.ok) {
      throw new Error(`服务器响应错误: ${response.status}`);
    }
    console.log('✅ 服务器运行正常\n');
  } catch (error) {
    console.error('❌ 服务器不可访问:', error.message);
    console.log('💡 请先启动服务器: npm start 或 pm2 start ecosystem.config.cjs');
    process.exit(1);
  }

  // 执行各项测试
  try {
    await testErrorResponses();
    await testCircuitBreaker();
    await testConcurrencyControl();
    await testHealthCheckIntegration();
    await testSpecificErrorScenarios();
    await testRetryDelayMechanism();
  } catch (error) {
    console.error('\n❌ 测试执行过程中发生错误:', error);
    recordTestResult('测试执行', false, error.message);
  }

  // 输出测试结果
  printTestResults();
}

// 打印测试结果
function printTestResults() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试结果汇总');
  console.log('='.repeat(60));

  console.log(`\n📈 总体统计:`);
  console.log(`   总测试数: ${testResults.total}`);
  console.log(`   通过: ${testResults.passed} ✅`);
  console.log(`   失败: ${testResults.failed} ❌`);
  console.log(`   成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);

  if (testResults.failed > 0) {
    console.log(`\n❌ 失败的测试:`);
    testResults.details
      .filter(test => !test.passed)
      .forEach(test => {
        console.log(`   • ${test.name}`);
        if (test.details) {
          console.log(`     ${test.details}`);
        }
      });
  }

  console.log(`\n🎯 重试机制测试重点:`);
  console.log(`   ✓ 401/403错误不重试 - 避免无效重试`);
  console.log(`   ✓ quota_exceeded不重试 - 避免配额浪费`);
  console.log(`   ✓ 网络错误重试 - 提高可用性`);
  console.log(`   ✓ 电路熔断器保护 - 防止雪崩`);
  console.log(`   ✓ 并发控制 - 避免过载`);

  if (testResults.passed === testResults.total) {
    console.log(`\n🎉 所有测试通过！重试机制工作正常。`);
    process.exit(0);
  } else {
    console.log(`\n⚠️ 部分测试失败，请检查重试机制配置。`);
    process.exit(1);
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  recordTestResult('未处理错误', false, reason.toString());
  printTestResults();
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  recordTestResult('未捕获异常', false, error.message);
  printTestResults();
});

// 执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}
