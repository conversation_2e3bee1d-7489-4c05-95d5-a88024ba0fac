# 🧪 重试机制测试文档

## 📋 概述

本测试套件专门针对项目中的重试机制进行全面测试，重点验证401、403、"status":"quota_exceeded"等场景的处理逻辑，确保系统在各种错误情况下的行为符合预期。

## 🎯 测试目标

### 核心验证点
- ✅ **401 Unauthorized** - 认证失败不应重试
- ✅ **403 Forbidden** - 权限不足不应重试  
- ✅ **429 Quota Exceeded** - 配额超限不应重试
- ✅ **网络错误** - 应该触发重试机制
- ✅ **电路熔断器** - 故障保护和自动恢复
- ✅ **并发控制** - 防止系统过载
- ✅ **重试延迟** - 指数退避策略验证

## 📁 测试文件结构

```
├── test-retry-mechanisms.js    # 重试机制综合测试
├── test-error-scenarios.js     # 错误场景专项测试
├── run-retry-tests.js          # 测试运行器
└── RETRY_TESTS_README.md       # 本文档
```

## 🚀 快速开始

### 1. 启动服务器
```bash
# 开发环境
npm start

# 或生产环境
pm2 start ecosystem.config.cjs
```

### 2. 运行测试

#### 运行所有重试测试
```bash
npm run test:all
```

#### 运行特定测试
```bash
# 错误场景专项测试
npm run test:errors

# 重试机制综合测试  
npm run test:retry

# 原有的基础测试
npm test
```

#### 直接运行测试文件
```bash
# 错误场景测试
node test-error-scenarios.js

# 重试机制测试
node test-retry-mechanisms.js
```

## 🔍 测试详情

### 1. 错误场景专项测试 (`test-error-scenarios.js`)

#### 测试用例
- **401 Unauthorized 测试**
  - 使用无效密钥发送请求
  - 验证快速失败（不重试）
  - 检查错误信息格式

- **403 Forbidden 测试**
  - 模拟权限不足场景
  - 验证不触发重试机制
  - 确认响应时间合理

- **429 Quota Exceeded 测试**
  - 发送大量并发请求
  - 检测限流保护机制
  - 验证retryAfter信息

- **网络错误测试**
  - 模拟连接失败
  - 验证重试触发条件
  - 检查超时处理

#### 预期结果
```
📊 错误场景测试结果
====================================
📈 统计信息:
   总测试: 15
   通过: 15 ✅
   失败: 0 ❌
   成功率: 100%
```

### 2. 重试机制综合测试 (`test-retry-mechanisms.js`)

#### 测试组件
- **电路熔断器测试**
  - 状态转换验证 (CLOSED → OPEN → HALF_OPEN)
  - 失败阈值测试
  - 自动恢复机制

- **并发控制测试**
  - 多请求并发发送
  - 限流机制验证
  - 响应时间分析

- **健康检查集成**
  - 电路熔断器状态监控
  - 统计信息验证
  - 系统健康评分

#### 预期结果
```
📊 重试机制测试结果
====================================
📈 统计信息:
   总测试: 20
   通过: 20 ✅
   失败: 0 ❌
   成功率: 100%
```

## ⚙️ 配置说明

### 环境变量
```bash
# 测试目标服务器
TEST_BASE_URL=http://localhost:3007

# 认证密钥
PROXY_SECRET=AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9

# 电路熔断器配置
CIRCUIT_BREAKER_THRESHOLD=3
CIRCUIT_BREAKER_TIMEOUT=30000

# 动态地区功能
ENABLE_DYNAMIC_REGIONS=true
```

### 测试配置
```javascript
const TEST_CONFIG = {
  BASE_URL: 'http://localhost:3007',
  PROXY_SECRET: 'your_secret_key',
  TEST_VOICE_ID: 'test_voice_id',
  TIMEOUT: 10000
};
```

## 📊 测试报告示例

### 成功运行示例
```
🧪 重试机制测试套件
==================================================
📍 测试目标: http://localhost:3007
🔑 认证密钥: AKID...
⏱️  超时设置: 30000ms
==================================================

🔍 检查服务器状态...
✅ 服务器运行正常

🚀 运行: 错误场景专项测试
📄 文件: test-error-scenarios.js
📝 描述: 测试401、403、quota_exceeded等错误场景
----------------------------------------
   ✅ 401错误状态码 (1250ms)
   ✅ 401错误响应时间 (1250ms)
   ✅ 401错误信息 (1250ms)
   ✅ 403或其他客户端错误 (890ms)
   ✅ 限流或熔断保护 (2340ms)
   ✅ 网络错误检测 (3010ms)
   ✅ 电路熔断器状态可见 (45ms)
✅ 错误场景专项测试 - 通过

🚀 运行: 重试机制综合测试
📄 文件: test-retry-mechanisms.js
📝 描述: 测试电路熔断器、并发控制、重试逻辑
----------------------------------------
   ✅ 电路熔断器OPEN状态 (120ms)
   ✅ OPEN状态请求拒绝 (5ms)
   ✅ 电路熔断器恢复 (5150ms)
   ✅ 并发控制响应 (1890ms)
   ✅ 健康检查包含电路熔断器信息 (67ms)
✅ 重试机制综合测试 - 通过

====================================
📊 重试机制测试套件 - 最终结果
====================================

📈 总体统计:
   测试文件总数: 2
   通过: 2 ✅
   失败: 0 ❌
   成功率: 100%

🎉 所有测试通过！重试机制工作正常。
```

## 🔧 故障排除

### 常见问题

#### 1. 服务器连接失败
```
❌ 无法连接到服务器: ECONNREFUSED
```
**解决方案:**
- 确认服务器正在运行: `npm start`
- 检查端口配置: 默认3007
- 验证防火墙设置

#### 2. 认证失败
```
❌ 401错误状态码 - 失败
```
**解决方案:**
- 检查PROXY_SECRET环境变量
- 确认.env文件配置正确
- 验证密钥格式

#### 3. 测试超时
```
❌ 测试超时: 重试机制综合测试
```
**解决方案:**
- 增加超时时间配置
- 检查网络连接稳定性
- 确认服务器响应正常

#### 4. 电路熔断器测试失败
```
❌ 电路熔断器OPEN状态 - 失败
```
**解决方案:**
- 检查CIRCUIT_BREAKER_THRESHOLD配置
- 确认电路熔断器功能启用
- 查看服务器日志

## 📈 测试覆盖率

### HTTP状态码覆盖
- ✅ 200 OK - 成功响应
- ✅ 401 Unauthorized - 认证失败
- ✅ 403 Forbidden - 权限不足
- ✅ 413 Payload Too Large - 请求体过大
- ✅ 429 Too Many Requests - 限流
- ✅ 500 Internal Server Error - 服务器错误
- ✅ 502 Bad Gateway - 网关错误
- ✅ 503 Service Unavailable - 服务不可用

### 网络错误覆盖
- ✅ ECONNREFUSED - 连接被拒绝
- ✅ ENOTFOUND - 域名解析失败
- ✅ ECONNRESET - 连接重置
- ✅ ETIMEDOUT - 连接超时

### 重试机制覆盖
- ✅ 可重试错误识别
- ✅ 不可重试错误识别
- ✅ 重试次数限制
- ✅ 重试延迟策略
- ✅ 电路熔断器集成

## 🎯 最佳实践

### 1. 测试前准备
- 确保服务器正常运行
- 验证环境变量配置
- 检查代理服务器连接

### 2. 测试执行
- 使用`npm run test:all`运行完整测试
- 单独运行失败的测试进行调试
- 查看详细的测试输出

### 3. 结果分析
- 关注成功率和响应时间
- 检查错误信息的准确性
- 验证重试行为符合预期

### 4. 持续集成
- 将测试集成到CI/CD流程
- 设置测试失败时的告警
- 定期运行测试确保稳定性

## 📚 相关文档

- [重试机制实现文档](./ROBUST_ERROR_HANDLING_IMPLEMENTATION.md)
- [电路熔断器配置](./lib/utils.js)
- [API错误处理](./api/v1/text-to-speech/tts.js)
- [健康检查集成](./api/health.js)
