好的，非常棒！您提供了一个已经存在的、基于Express.js的代理服务器项目。这使得我们的改造工作有了坚实的基础，大大简化了实施过程。

经过仔细审查您提供的所有文件 (package.json, server.js, ecosystem.config.cjs, lib/utils.js, api/health.js, api/v1/text-to-speech/tts.js)，我确认：我们完全可以在这个现有项目的基础上进行改造，并且大部分核心代码（如代理配置、安全验证）都可以复用。

这个现有项目目前是一个无状态的流式代理，我们的目标是把它改造成一个有状态的、支持分片处理和合并的代理。

下面是详细的改造方案。

方案：将现有EC2代理服务改造为“有状态”的TTS处理中枢

我们将保留现有的大部分结构，重点是新增一个用于处理分片的端点，并引入任务跟踪、文件存储和合并的逻辑。

第一步：安装新依赖

您现有的 package.json 缺少文件操作和音频合并的库。请在您的EC2项目目录下执行：

Generated bash
npm install fluent-ffmpeg@2.1.2 ffmpeg-static@5.2.0 fs-extra@11.2.0


fluent-ffmpeg: 一个强大的FFmpeg封装库，用于合并音频。

ffmpeg-static: 提供一个静态的、可执行的FFmpeg二进制文件，无需在服务器上手动安装FFmpeg。

fs-extra: 提供了比Node.js原生fs模块更方便的文件系统操作方法（如ensureDir, remove）。

第二步：修改 server.js - 新增端点和状态管理

我们需要在主服务文件中添加新的端点，并初始化任务跟踪器。

Generated javascript
// server.js

// ... (保留顶部的 import)
import { checkProxySecret } from './lib/utils.js'; // 我们需要这个
import fs from 'fs-extra';
import path from 'path';

// 【新增】引入新的分片处理器
import chunkHandler from './api/v1/process_chunk.js';

const app = express();
const PORT = process.env.PORT || 3002;

app.use(express.json({ limit: '10mb' }));
// 注意：移除了 express.raw()，因为我们的新端点都用json

console.log('🚀 Ubuntu Stateful TTS Proxy Server starting...');

// 【新增】任务状态跟踪和临时目录设置
export const TEMP_DIR = path.join(process.cwd(), 'temp_tts');
fs.ensureDirSync(TEMP_DIR);
export const taskTracker = {}; // 简单的内存跟踪器

// ... (保留根路径 '/' 和健康检查 '/api/health' 的路由)
app.all('/api/health', healthHandler);


// 【保留】旧的TTS代理路由 - 可用于简单的、非分片任务，或作为调试
app.all('/api/v1/text-to-speech/:voice_id', ttsHandler);


// 【新增】处理分片的核心路由
app.post('/api/v1/process_chunk', chunkHandler);


// 【新增】任务完成后的回调端点（这个是代理内部逻辑，不需要暴露给外部）
// 这部分逻辑将放在新的 process_chunk.js 文件中处理


// ... (保留 404 和全局错误处理中间件)

// ... (保留服务器启动和优雅关闭逻辑)

export default app;
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

改动小结:

引入新依赖fs-extra和path。

引入一个新的处理器 chunkHandler。

初始化 TEMP_DIR 和 taskTracker 并导出，以便其他模块使用。

注册新的路由 POST /api/v1/process_chunk。

第三步：创建新的核心处理器 api/v1/process_chunk.js

这是改造的核心。新建一个文件 api/v1/process_chunk.js。这个文件将包含我们之前讨论的所有有状态逻辑。

Generated javascript
// api/v1/process_chunk.js

import fetch from 'node-fetch';
import ffmpeg from 'fluent-ffmpeg';
import ffmpegPath from 'ffmpeg-static';
import { TEMP_DIR, taskTracker } from '../../server.js';
import { checkProxySecret, callElevenLabsAPI } from '../../lib/utils.js';
import fs from 'fs-extra';
import path from 'path';

ffmpeg.setFfmpegPath(ffmpegPath);

const WORKER_CALLBACK_URL = process.env.WORKER_CALLBACK_URL; // 从.env读取回调URL
const WORKER_CALLBACK_SECRET = process.env.WORKER_CALLBACK_SECRET; // 从.env读取回调密钥

// 主处理器
export default async function chunkHandler(req, res) {
    // 1. 安全验证
    const authResult = checkProxySecret(req);
    if (!authResult.isValid) {
        return res.status(401).json(authResult.error);
    }

    const { taskId, chunkIndex, totalChunks, voiceId, tts_payload } = req.body;

    if (!taskId || chunkIndex === undefined || !totalChunks || !voiceId || !tts_payload) {
        return res.status(400).json({ error: 'Missing required parameters for chunk processing' });
    }

    console.log(`[${taskId}] Received chunk ${chunkIndex + 1}/${totalChunks}`);

    try {
        // 2. 初始化任务跟踪
        const taskDir = path.join(TEMP_DIR, taskId);
        if (!taskTracker[taskId]) {
            taskTracker[taskId] = {
                completedChunks: 0,
                totalChunks: totalChunks,
                status: 'processing',
                failedChunks: []
            };
            await fs.ensureDir(taskDir);
        }

        // 3. 调用ElevenLabs API (复用utils中的函数)
        const requestBody = JSON.stringify(tts_payload);
        const elevenLabsResponse = await callElevenLabsAPI(voiceId, requestBody);

        if (!elevenLabsResponse.ok) {
            const errorBody = await elevenLabsResponse.text();
            throw new Error(`ElevenLabs API failed with status ${elevenLabsResponse.status}: ${errorBody}`);
        }

        const audioBuffer = await elevenLabsResponse.buffer();

        // 4. 临时存储分片到硬盘
        const chunkPath = path.join(taskDir, `${chunkIndex}.mp3`);
        await fs.writeFile(chunkPath, audioBuffer);
        console.log(`[${taskId}] Saved chunk ${chunkIndex + 1} to ${chunkPath}`);
        
        // 5. 更新任务进度
        taskTracker[taskId].completedChunks++;
        
        // 6. 立即响应Worker
        res.status(200).json({ status: 'chunk_received', taskId, chunkIndex });

        // 7. 检查任务是否全部完成 (异步进行)
        if (taskTracker[taskId].completedChunks === taskTracker[taskId].totalChunks) {
            console.log(`[${taskId}] All chunks received. Starting merge process.`);
            taskTracker[taskId].status = 'merging';
            
            mergeAndCallback(taskId); 
        }

    } catch (error) {
        console.error(`[${taskId}] Error processing chunk ${chunkIndex + 1}:`, error.message);
        if (taskTracker[taskId]) {
            taskTracker[taskId].failedChunks.push(chunkIndex);
        }
        res.status(500).json({ status: 'chunk_failed', taskId, chunkIndex, error: error.message });
    }
}

// 合并与回调的辅助函数
async function mergeAndCallback(taskId) {
    const taskDir = path.join(TEMP_DIR, taskId);
    const finalAudioPath = path.join(taskDir, 'final.mp3');
    const fileListPath = path.join(taskDir, 'filelist.txt');

    try {
        const { totalChunks } = taskTracker[taskId];
        
        const fileList = Array.from({ length: totalChunks }, (_, i) => `file '${i}.mp3'`).join('\n');
        await fs.writeFile(fileListPath, fileList);

        await new Promise((resolve, reject) => {
            ffmpeg(fileListPath)
                .inputOptions(['-f', 'concat', '-safe', '0'])
                .outputOptions('-c', 'copy')
                .save(finalAudioPath)
                .on('end', () => {
                    console.log(`[${taskId}] FFmpeg merge successful.`);
                    resolve();
                })
                .on('error', (err) => {
                    console.error(`[${taskId}] FFmpeg merge error:`, err);
                    reject(err);
                });
        });

        taskTracker[taskId].status = 'merged';
        const finalAudioBuffer = await fs.readFile(finalAudioPath);

        console.log(`[${taskId}] Sending final audio (${(finalAudioBuffer.length / 1024).toFixed(2)} KB) to Worker...`);
        
        if (!WORKER_CALLBACK_URL) {
            throw new Error("WORKER_CALLBACK_URL is not configured in .env file.");
        }
        
        const callbackResponse = await fetch(WORKER_CALLBACK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/octet-stream',
                'x-callback-secret': WORKER_CALLBACK_SECRET,
                'x-task-id': taskId
            },
            body: finalAudioBuffer
        });

        if (!callbackResponse.ok) {
            const errorText = await callbackResponse.text();
            throw new Error(`Worker callback failed with status ${callbackResponse.status}: ${errorText}`);
        }
        
        console.log(`[${taskId}] Worker callback successful.`);
        taskTracker[taskId].status = 'completed';

    } catch (error) {
        console.error(`[${taskId}] Merge or callback process failed:`, error.message);
        if (taskTracker[taskId]) taskTracker[taskId].status = 'failed';
        // TODO: 在这里可以增加失败回调的逻辑，通知Worker任务失败
    } finally {
        await fs.remove(taskDir);
        delete taskTracker[taskId];
        console.log(`[${taskId}] Cleaned up temporary files.`);
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
第四步：更新环境变量配置 .env 和 ecosystem.config.cjs

您需要添加新的环境变量来支持回调。

.env 文件:

Generated env
# ... (保留原有的 PROXY_IP, PORT, USERNAME, PASSWORD, PROXY_SECRET)
# PROXY_SECRET 是 Worker -> Proxy 的密钥

# 【新增】
WORKER_CALLBACK_URL="https://<你的Worker域名>/api/tts/task-complete"
WORKER_CALLBACK_SECRET="<一个用于Proxy->Worker回调的独立、复杂的密钥>"
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Env
IGNORE_WHEN_COPYING_END

ecosystem.config.cjs 文件:
这个文件很好，它已经配置为使用.env，所以您不需要修改它，只需确保服务器上存在包含新变量的.env文件即可。

第五步：对现有 lib/utils.js 进行微调

callElevenLabsAPI 函数已经非常完美，几乎不需要修改，因为它已经使用了代理。我们只需要确保它能被新模块正确调用。

Worker端的代码修改

现在，我们回到 worker.js，应用之前讨论过的修改。

修改 generateSpeech 以发送新的分片负载。

修改 processChunks 不再期望返回音频。

修改 executeDialogueTtsCore 变为“分发-等待”模式。

在 handleRequest (主入口) 中新增 /api/tts/task-complete 端点。

在 TtsTaskDoProxyTest (DO) 中新增 handleProxyCallback 方法。

这些修改与我上一个回答中提供的Worker端代码完全一致，您可以直接采用。这里不再重复粘贴，以保持清晰。

实施总结与部署

在EC2上:

更新 package.json 的 dependencies。

运行 npm install。

创建新的 api/v1/process_chunk.js 文件并粘贴代码。

修改 server.js 以注册新路由和状态管理。

在服务器上创建或更新 .env 文件，添加 WORKER_CALLBACK_URL 和 WORKER_CALLBACK_SECRET。

使用 PM2 重启您的代理服务：pm2 reload ecosystem.config.cjs --env production。

在Cloudflare Worker上:

部署修改后的 worker.js 代码。

在Worker的环境变量中，添加 PROXY_TO_WORKER_SECRET，其值必须与EC2上 .env 文件中的 WORKER_CALLBACK_SECRET 相同。

最重要：将 FORCE_PROXY_ONLY_MODE 设置为 true 来激活这套新的有状态代理流程。

完成这些步骤后，您的系统就成功地升级为了一个高效、健壮、可扩展的混合架构TTS服务。