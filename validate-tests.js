#!/usr/bin/env node
// validate-tests.js - 验证测试文件语法和基本功能

import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔍 验证重试测试文件...\n');

// 要验证的测试文件
const TEST_FILES = [
  'test-retry-mechanisms.js',
  'test-error-scenarios.js',
  'run-retry-tests.js'
];

// 验证结果
let validationResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

// 验证单个文件
async function validateFile(filename) {
  console.log(`📄 验证文件: ${filename}`);
  
  try {
    // 检查文件是否存在和可读
    const filePath = join(__dirname, filename);
    const content = await readFile(filePath, 'utf8');
    
    // 基本语法检查
    const checks = [
      {
        name: '文件可读取',
        test: () => content.length > 0,
        details: `文件大小: ${content.length} 字符`
      },
      {
        name: 'ES模块语法',
        test: () => content.includes('import') && !content.includes('require('),
        details: '使用ES模块导入语法'
      },
      {
        name: '包含测试函数',
        test: () => content.includes('async function test') || content.includes('function test'),
        details: '包含测试函数定义'
      },
      {
        name: '错误处理',
        test: () => content.includes('try') && content.includes('catch'),
        details: '包含错误处理逻辑'
      },
      {
        name: '配置对象',
        test: () => content.includes('CONFIG') || content.includes('TEST_CONFIG'),
        details: '包含测试配置'
      },
      {
        name: '结果记录',
        test: () => content.includes('record') || content.includes('collector'),
        details: '包含结果记录机制'
      }
    ];

    // 特定文件的额外检查
    if (filename === 'test-error-scenarios.js') {
      checks.push(
        {
          name: '401测试',
          test: () => content.includes('401') && content.includes('Unauthorized'),
          details: '包含401错误测试'
        },
        {
          name: '403测试',
          test: () => content.includes('403') && content.includes('Forbidden'),
          details: '包含403错误测试'
        },
        {
          name: 'Quota测试',
          test: () => content.includes('quota') || content.includes('429'),
          details: '包含配额超限测试'
        }
      );
    }

    if (filename === 'test-retry-mechanisms.js') {
      checks.push(
        {
          name: '电路熔断器',
          test: () => content.includes('CircuitBreaker') || content.includes('circuit'),
          details: '包含电路熔断器测试'
        },
        {
          name: '重试逻辑',
          test: () => content.includes('retry') || content.includes('重试'),
          details: '包含重试机制测试'
        }
      );
    }

    // 执行检查
    let fileResults = { passed: 0, failed: 0, details: [] };
    
    for (const check of checks) {
      try {
        const result = check.test();
        if (result) {
          fileResults.passed++;
          console.log(`   ✅ ${check.name}`);
        } else {
          fileResults.failed++;
          console.log(`   ❌ ${check.name}`);
        }
        fileResults.details.push({
          name: check.name,
          passed: result,
          details: check.details
        });
      } catch (error) {
        fileResults.failed++;
        console.log(`   ❌ ${check.name} - 检查失败: ${error.message}`);
        fileResults.details.push({
          name: check.name,
          passed: false,
          details: `检查失败: ${error.message}`
        });
      }
    }

    // 尝试语法验证（通过动态导入）
    try {
      console.log(`   🔍 语法验证...`);
      // 注意：这里不实际导入，只是检查基本语法
      if (content.includes('export') && content.includes('import')) {
        console.log(`   ✅ ES模块语法正确`);
        fileResults.passed++;
      } else {
        console.log(`   ⚠️ 可能的语法问题`);
        fileResults.failed++;
      }
    } catch (error) {
      console.log(`   ❌ 语法验证失败: ${error.message}`);
      fileResults.failed++;
    }

    validationResults.details.push({
      filename,
      ...fileResults
    });

    console.log(`   📊 ${filename}: ${fileResults.passed}通过 / ${fileResults.failed}失败\n`);
    
    return fileResults.failed === 0;

  } catch (error) {
    console.error(`   ❌ 验证失败: ${error.message}\n`);
    validationResults.details.push({
      filename,
      passed: 0,
      failed: 1,
      error: error.message
    });
    return false;
  }
}

// 主验证函数
async function validateAllTests() {
  console.log('🚀 开始验证测试文件...\n');
  
  validationResults.total = TEST_FILES.length;
  
  for (const filename of TEST_FILES) {
    const isValid = await validateFile(filename);
    if (isValid) {
      validationResults.passed++;
    } else {
      validationResults.failed++;
    }
  }

  // 输出总结
  printValidationSummary();
}

// 打印验证总结
function printValidationSummary() {
  console.log('='.repeat(50));
  console.log('📊 测试文件验证结果');
  console.log('='.repeat(50));

  console.log(`\n📈 总体统计:`);
  console.log(`   验证文件数: ${validationResults.total}`);
  console.log(`   通过: ${validationResults.passed} ✅`);
  console.log(`   失败: ${validationResults.failed} ❌`);
  console.log(`   成功率: ${Math.round((validationResults.passed / validationResults.total) * 100)}%`);

  console.log(`\n📋 详细结果:`);
  validationResults.details.forEach((result, index) => {
    const status = result.failed === 0 ? '✅' : '❌';
    console.log(`   ${index + 1}. ${status} ${result.filename}`);
    if (result.error) {
      console.log(`      错误: ${result.error}`);
    } else {
      console.log(`      检查项: ${result.passed}通过 / ${result.failed}失败`);
    }
  });

  if (validationResults.failed > 0) {
    console.log(`\n❌ 验证失败的文件:`);
    validationResults.details
      .filter(r => r.failed > 0)
      .forEach(r => {
        console.log(`   • ${r.filename}`);
        if (r.details) {
          r.details
            .filter(d => !d.passed)
            .forEach(d => {
              console.log(`     - ${d.name}: ${d.details}`);
            });
        }
      });
  }

  console.log(`\n🎯 验证要点:`);
  console.log(`   ✓ 文件语法正确性`);
  console.log(`   ✓ ES模块导入导出`);
  console.log(`   ✓ 测试函数完整性`);
  console.log(`   ✓ 错误处理机制`);
  console.log(`   ✓ 配置和结果记录`);

  if (validationResults.passed === validationResults.total) {
    console.log(`\n🎉 所有测试文件验证通过！可以安全运行测试。`);
    console.log(`\n💡 下一步:`);
    console.log(`   1. 启动服务器: npm start`);
    console.log(`   2. 运行测试: npm run test:all`);
    console.log(`   3. 查看测试结果和报告`);
    process.exit(0);
  } else {
    console.log(`\n⚠️ 部分文件验证失败，请修复后再运行测试。`);
    console.log(`\n🔧 修复建议:`);
    console.log(`   1. 检查文件语法错误`);
    console.log(`   2. 确认导入导出语句正确`);
    console.log(`   3. 验证测试函数完整性`);
    console.log(`   4. 添加必要的错误处理`);
    process.exit(1);
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('\n❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('\n❌ 未捕获的异常:', error);
  process.exit(1);
});

// 执行验证
if (import.meta.url === `file://${process.argv[1]}`) {
  validateAllTests().catch(error => {
    console.error('❌ 验证过程失败:', error);
    process.exit(1);
  });
}

export { validateAllTests };
