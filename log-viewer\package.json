{"name": "tts-proxy-log-viewer", "version": "1.0.0", "description": "简单的Web日志查看器，用于查看TTS代理服务的实时日志", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "basic-auth": "^2.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["log-viewer", "websocket", "real-time", "monitoring"], "author": "", "license": "ISC"}