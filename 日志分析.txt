0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [TTS] req_1752940660856_7 Processing request for voice_id: pNInz6obpgDQGcFmaJgB
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [SECURITY] ✅ Proxy secret validation successful
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [CONCURRENCY] ✅ Request allowed for pNInz6obpgDQGcFmaJgB (concurrency control disabled)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [CONCURRENCY] 📊 Tracking req_1752940660856_7 for pNInz6obpgDQGcFmaJgB (1 active, no limits)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [TTS] req_1752940660856_7 Request body prepared, calling ElevenLabs API...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [FINGERPRINT] 🎭 New session pNInz6obpgDQGcFmaJgB_d1f40ca7 assigned profile: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [FINGERPRINT] 📊 Active sessions: 1
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:40 +00:00: [FINGERPRINT] ⏱️ Adding human-like delay: 1330ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🔄 Starting API call with dynamic regions (max retries: 3)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🎯 Attempt 1/3
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] ✅ Loaded 3914 regions from file
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📊 Sample regions: US_Alabama_Houston, US_Alabama_Jackson, US_Alabama_Madison
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🎲 Selected random region: AU_Victoria_city_Sale
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📊 Selected from 3914 available regions (index: 2098)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🔐 Built dynamic proxy auth:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Username: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Password: 14ac4e67-AU_Victoria_city_Sale
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Region: AU_Victoria_city_Sale
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 🌐 Attempt 1: Using IP proxy gate.kookeey.info:1000 for ElevenLabs API
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 📡 Proxy URL: http://gate.kookeey.info:1000
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 🔐 Auth Method: Proxy-Authorization header (用户名: 4627768-8c4b0cb7)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 🎯 Target URL: https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] ⏱️  Timeout: 180000ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [FINGERPRINT] 🎭 Using browser profile: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [FINGERPRINT] 🔑 Session ID: pNInz6obpgDQGcFmaJgB_d1f40ca7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🎲 Creating dynamic proxy agent with random region
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🎲 Selected random region: TH_UthaiThani_city_LanSak
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 📊 Selected from 3914 available regions (index: 2919)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🔐 Built dynamic proxy auth:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Username: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Password: 14ac4e67-TH_UthaiThani_city_LanSak
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Region: TH_UthaiThani_city_LanSak
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] ✅ 初始化动态地区代理配置
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🌐 选中地区: TH_UthaiThani_city_LanSak
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🔐 动态认证: 4627768-8c4b0cb7:14ac4e67...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 🌐 代理服务器: gate.kookeey.info:1000
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 👤 用户名: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 🔒 使用优化SSL配置适配IP代理
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [PROXY] 🔐 采用分离式认证方式 (curl官方标准)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] ❌ Attempt 1 failed for voice pNInz6obpgDQGcFmaJgB
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Error Name: FetchError
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Error Message: request to https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 failed, reason: write EPROTO 0078E4AAEC780000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:../deps/openssl/openssl/ssl/record/ssl3_record.c:355:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: 
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Error Code: EPROTO
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION]    Stack Trace: FetchError: request to https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 failed, reason: write EPROTO 0078E4AAEC780000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:../deps/openssl/openssl/ssl/record/ssl3_record.c:355:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: 
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at ClientRequest.<anonymous> (file:///var/www/tts-proxy-vps-401-403/node_modules/node-fetch/src/index.js:108:11)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at ClientRequest.emit (node:events:529:35)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at TLSSocket.socketErrorListener (node:_http_client:501:9)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at TLSSocket.emit (node:events:517:28)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at emitErrorNT (node:internal/streams/destroy:151:8)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at emitErrorCloseNT (node:internal/streams/destroy:116:3)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 🚫 No more retries. Reason: Non-retryable error
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [REGION] 💥 All attempts failed. Last error: request to https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 failed, reason: write EPROTO 0078E4AAEC780000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:../deps/openssl/openssl/ssl/record/ssl3_record.c:355:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: 
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [CIRCUIT] ❌ Operation failed (2/800): request to https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 failed, reason: write EPROTO 0078E4AAEC780000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:../deps/openssl/openssl/ssl/record/ssl3_record.c:355:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: 
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [TTS] req_1752940660856_7 ❌ Internal error: FetchError: request to https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 failed, reason: write EPROTO 0078E4AAEC780000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:../deps/openssl/openssl/ssl/record/ssl3_record.c:355:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: 
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at ClientRequest.<anonymous> (file:///var/www/tts-proxy-vps-401-403/node_modules/node-fetch/src/index.js:108:11)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at ClientRequest.emit (node:events:529:35)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at TLSSocket.socketErrorListener (node:_http_client:501:9)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at TLSSocket.emit (node:events:517:28)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at emitErrorNT (node:internal/streams/destroy:151:8)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at emitErrorCloseNT (node:internal/streams/destroy:116:3)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:     at process.processTicksAndRejections (node:internal/process/task_queues:82:21) {
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:   type: 'system',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:   errno: 'EPROTO',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:   code: 'EPROTO',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00:   erroredSysCall: 'write'
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: }
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:42 +00:00: [CONCURRENCY] 📊 Completed req_1752940660856_7 for pNInz6obpgDQGcFmaJgB (0 remaining)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:45 +00:00: [TTS] req_1752940665864_8 Processing request for voice_id: pNInz6obpgDQGcFmaJgB
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:45 +00:00: [SECURITY] ✅ Proxy secret validation successful
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:45 +00:00: [CONCURRENCY] ✅ Request allowed for pNInz6obpgDQGcFmaJgB (concurrency control disabled)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:45 +00:00: [CONCURRENCY] 📊 Tracking req_1752940665864_8 for pNInz6obpgDQGcFmaJgB (1 active, no limits)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:45 +00:00: [TTS] req_1752940665864_8 Request body prepared, calling ElevenLabs API...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:45 +00:00: [FINGERPRINT] 🔄 Using existing profile for session pNInz6obpgDQGcFmaJgB_d1f40ca7: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:45 +00:00: [FINGERPRINT] ⏱️ Adding human-like delay: 1828ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🔄 Starting API call with dynamic regions (max retries: 3)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🎯 Attempt 1/3
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🎲 Selected random region: TH_Chaiyaphum_city_PhakdiChumphon
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📊 Selected from 3914 available regions (index: 2812)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🔐 Built dynamic proxy auth:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION]    Username: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION]    Password: 14ac4e67-TH_Chaiyaphum_city_PhakdiChumphon
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION]    Region: TH_Chaiyaphum_city_PhakdiChumphon
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 🌐 Attempt 1: Using IP proxy gate.kookeey.info:1000 for ElevenLabs API
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 📡 Proxy URL: http://gate.kookeey.info:1000
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 🔐 Auth Method: Proxy-Authorization header (用户名: 4627768-8c4b0cb7)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 🎯 Target URL: https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] ⏱️  Timeout: 180000ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [FINGERPRINT] 🎭 Using browser profile: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [FINGERPRINT] 🔑 Session ID: pNInz6obpgDQGcFmaJgB_d1f40ca7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🎲 Creating dynamic proxy agent with random region
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🎲 Selected random region: AR_SantaFe_city_LasRosas
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 📊 Selected from 3914 available regions (index: 2170)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🔐 Built dynamic proxy auth:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION]    Username: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION]    Password: 14ac4e67-AR_SantaFe_city_LasRosas
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION]    Region: AR_SantaFe_city_LasRosas
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] ✅ 初始化动态地区代理配置
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🌐 选中地区: AR_SantaFe_city_LasRosas
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [REGION] 🔐 动态认证: 4627768-8c4b0cb7:14ac4e67...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 🌐 代理服务器: gate.kookeey.info:1000
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 👤 用户名: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 🔒 使用优化SSL配置适配IP代理
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:47 +00:00: [PROXY] 🔐 采用分离式认证方式 (curl官方标准)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] ✅ Proxy request successful!
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] 📊 Response status: 403
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] ⚡ Response time: 3894ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] 🔗 Content-Type: application/json
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [FINGERPRINT] 📈 Session pNInz6obpgDQGcFmaJgB_d1f40ca7 request count: 1
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] ⚠️ Non-2xx status code received: 403
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] 📋 Response headers: {
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'access-control-allow-headers': '*',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'access-control-allow-origin': '*',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'access-control-max-age': '600',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   connection: 'close',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'content-encoding': 'gzip',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'content-type': 'application/json',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   date: 'Sat, 19 Jul 2025 15:57:49 GMT',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   server: 'uvicorn',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'strict-transport-security': 'max-age=31536000; includeSubDomains',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'transfer-encoding': 'chunked',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   vary: 'Accept-Encoding',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   via: '1.1 google, 1.1 google',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'x-region': 'us-central1',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   'x-trace-id': '145851637d60b215ee07ff726f4e7854'
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: }
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [FINGERPRINT] 🚨 Failed request with profile: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] 📄 Error response body: {
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   "detail": {
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     "status": "content_against_policy",
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     "message": "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:   }
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: }
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [PROXY] 🔄 Throwing retryable HTTP error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION] ❌ Attempt 1 failed for voice pNInz6obpgDQGcFmaJgB
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION]    Error Name: Error
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION]    Error Message: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION]    HTTP Status Code: 403
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION]    HTTP Error Type: Forbidden (403)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION]    Retry Strategy: Short delay (1-3s), max 2 retries
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION]    Stack Trace: Error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at attemptElevenLabsAPICall (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:994:11)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async callElevenLabsAPI (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:1073:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:109:14
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async CircuitBreaker.execute (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:377:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async robustTtsHandler (file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:108:32)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION] 🚫 Content policy violation detected - not retrying
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION] 📄 Returning original error message to upstream
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [REGION] 💥 All attempts failed. Last error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [CIRCUIT] 🚫 Content policy violation detected - not counting as circuit failure
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [TTS] req_1752940665864_8 ❌ Internal error: Error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at attemptElevenLabsAPICall (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:994:11)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async callElevenLabsAPI (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:1073:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:109:14
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async CircuitBreaker.execute (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:377:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00:     at async robustTtsHandler (file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:108:32)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [TTS] req_1752940665864_8 🚫 Content policy violation - returning original error
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:51 +00:00: [CONCURRENCY] 📊 Completed req_1752940665864_8 for pNInz6obpgDQGcFmaJgB (0 remaining)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:55 +00:00: [TTS] req_1752940675416_9 Processing request for voice_id: pNInz6obpgDQGcFmaJgB
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:55 +00:00: [SECURITY] ✅ Proxy secret validation successful
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:55 +00:00: [CONCURRENCY] ✅ Request allowed for pNInz6obpgDQGcFmaJgB (concurrency control disabled)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:55 +00:00: [CONCURRENCY] 📊 Tracking req_1752940675416_9 for pNInz6obpgDQGcFmaJgB (1 active, no limits)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:55 +00:00: [TTS] req_1752940675416_9 Request body prepared, calling ElevenLabs API...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:55 +00:00: [FINGERPRINT] 🔄 Using existing profile for session pNInz6obpgDQGcFmaJgB_d1f40ca7: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:55 +00:00: [FINGERPRINT] ⏱️ Adding human-like delay: 1208ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🔄 Starting API call with dynamic regions (max retries: 3)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🎯 Attempt 1/3
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🎲 Selected random region: CA_Ontario_city_Forest
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📊 Selected from 3914 available regions (index: 1530)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🔐 Built dynamic proxy auth:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION]    Username: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION]    Password: 14ac4e67-CA_Ontario_city_Forest
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION]    Region: CA_Ontario_city_Forest
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 🌐 Attempt 1: Using IP proxy gate.kookeey.info:1000 for ElevenLabs API
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 📡 Proxy URL: http://gate.kookeey.info:1000
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 🔐 Auth Method: Proxy-Authorization header (用户名: 4627768-8c4b0cb7)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 🎯 Target URL: https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] ⏱️  Timeout: 180000ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [FINGERPRINT] 🎭 Using browser profile: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [FINGERPRINT] 🔑 Session ID: pNInz6obpgDQGcFmaJgB_d1f40ca7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🎲 Creating dynamic proxy agent with random region
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📋 Using cached regions data
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🎲 Selected random region: US_Florida_city_Hamilton
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 📊 Selected from 3914 available regions (index: 331)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🔐 Built dynamic proxy auth:
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION]    Username: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION]    Password: 14ac4e67-US_Florida_city_Hamilton
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION]    Region: US_Florida_city_Hamilton
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] ✅ 初始化动态地区代理配置
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🌐 选中地区: US_Florida_city_Hamilton
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [REGION] 🔐 动态认证: 4627768-8c4b0cb7:14ac4e67...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 🌐 代理服务器: gate.kookeey.info:1000
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 👤 用户名: 4627768-8c4b0cb7
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 🔒 使用优化SSL配置适配IP代理
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:56 +00:00: [PROXY] 🔐 采用分离式认证方式 (curl官方标准)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] ✅ Proxy request successful!
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] 📊 Response status: 403
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] ⚡ Response time: 3336ms
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] 🔗 Content-Type: application/json
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [FINGERPRINT] 📈 Session pNInz6obpgDQGcFmaJgB_d1f40ca7 request count: 2
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] ⚠️ Non-2xx status code received: 403
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] 📋 Response headers: {
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'access-control-allow-headers': '*',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'access-control-allow-methods': 'POST, PATCH, OPTIONS, DELETE, GET, PUT',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'access-control-allow-origin': '*',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'access-control-max-age': '600',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   connection: 'close',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'content-encoding': 'gzip',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'content-type': 'application/json',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   date: 'Sat, 19 Jul 2025 15:57:57 GMT',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   server: 'uvicorn',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'strict-transport-security': 'max-age=31536000; includeSubDomains',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'transfer-encoding': 'chunked',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   vary: 'Accept-Encoding',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   via: '1.1 google, 1.1 google',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'x-region': 'us-central1',
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   'x-trace-id': 'b96923316571fe16af7f26778611ba16'
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: }
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [FINGERPRINT] 🚨 Failed request with profile: Microsoft Edge 125 on Windows 11
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] 📄 Error response body: {
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   "detail": {
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     "status": "content_against_policy",
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     "message": "We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:   }
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: }
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [PROXY] 🔄 Throwing retryable HTTP error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}...
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION] ❌ Attempt 1 failed for voice pNInz6obpgDQGcFmaJgB
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION]    Error Name: Error
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION]    Error Message: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION]    HTTP Status Code: 403
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION]    HTTP Error Type: Forbidden (403)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION]    Retry Strategy: Short delay (1-3s), max 2 retries
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION]    Stack Trace: Error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at attemptElevenLabsAPICall (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:994:11)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async callElevenLabsAPI (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:1073:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:109:14
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async CircuitBreaker.execute (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:377:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async robustTtsHandler (file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:108:32)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION] 🚫 Content policy violation detected - not retrying
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION] 📄 Returning original error message to upstream
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [REGION] 💥 All attempts failed. Last error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [CIRCUIT] 🚫 Content policy violation detected - not counting as circuit failure
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [TTS] req_1752940675416_9 ❌ Internal error: Error: HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at attemptElevenLabsAPICall (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:994:11)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async callElevenLabsAPI (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:1073:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:109:14
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async CircuitBreaker.execute (file:///var/www/tts-proxy-vps-401-403/lib/utils.js:377:22)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00:     at async robustTtsHandler (file:///var/www/tts-proxy-vps-401-403/api/v1/text-to-speech/tts.js:108:32)
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [TTS] req_1752940675416_9 🚫 Content policy violation - returning original error
0|ubuntu-tts-proxy-401/403-01  | 2025-07-19 15:57:59 +00:00: [CONCURRENCY] 📊 Completed req_1752940675416_9 for pNInz6obpgDQGcFmaJgB (0 remaining)
