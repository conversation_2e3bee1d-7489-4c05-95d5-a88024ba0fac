// test-dynamic-enabled.js - 测试启用动态地区功能后的API调用

import fetch from 'node-fetch';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const BASE_URL = 'http://localhost:3006';
const PROXY_SECRET = process.env.PROXY_SECRET || 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9';

console.log('🧪 动态地区功能启用测试开始...\n');
console.log(`ENABLE_DYNAMIC_REGIONS: ${process.env.ENABLE_DYNAMIC_REGIONS}`);
console.log('');

// 测试函数
async function testTTSWithDynamicRegions(testName, requestNumber) {
  console.log(`📋 ${testName}`);
  
  try {
    const testPayload = {
      text: `Hello, this is test request number ${requestNumber} for dynamic region proxy testing.`,
      model_id: "eleven_monolingual_v1",
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5
      }
    };

    console.log(`发送请求 ${requestNumber}...`);
    const startTime = Date.now();

    const response = await fetch(`${BASE_URL}/api/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': PROXY_SECRET
      },
      body: JSON.stringify(testPayload),
      timeout: 30000 // 30秒超时
    });

    const responseTime = Date.now() - startTime;

    console.log(`响应状态: ${response.status} ${response.statusText}`);
    console.log(`响应时间: ${responseTime}ms`);
    console.log(`Content-Type: ${response.headers.get('Content-Type')}`);
    
    if (response.ok) {
      console.log('✅ API调用成功');
      const contentLength = response.headers.get('Content-Length');
      if (contentLength) {
        console.log(`音频数据大小: ${contentLength} bytes`);
      }
    } else {
      console.log('❌ API调用失败');
      const errorText = await response.text();
      console.log('错误信息:', errorText.substring(0, 200));
    }

  } catch (error) {
    console.log('❌ 请求异常:', error.message);
  }
  
  console.log('');
}

// 健康检查测试
async function testHealthEndpoint() {
  console.log('📋 健康检查测试');
  
  try {
    const response = await fetch(`${BASE_URL}/api/health`, {
      method: 'GET',
      headers: {
        'x-proxy-secret': PROXY_SECRET
      }
    });

    console.log(`响应状态: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 健康检查成功');
      console.log(`服务器运行时间: ${data.uptime?.human || 'N/A'}`);
      console.log(`内存使用: ${data.memory?.used || 'N/A'}MB / ${data.memory?.total || 'N/A'}MB`);
    } else {
      console.log('❌ 健康检查失败');
    }

  } catch (error) {
    console.log('❌ 健康检查异常:', error.message);
  }
  
  console.log('');
}

// 执行测试
async function runTests() {
  // 1. 健康检查
  await testHealthEndpoint();
  
  // 2. 多次动态地区测试（验证地区轮换）
  console.log('📋 动态地区功能测试 - 连续5次调用');
  console.log('每次调用应该使用不同的随机地区，请观察服务器日志中的地区信息\n');
  
  for (let i = 1; i <= 5; i++) {
    await testTTSWithDynamicRegions(`测试 ${i}: 动态地区API调用`, i);
    
    // 添加延迟避免过快请求，便于观察日志
    if (i < 5) {
      console.log('等待 3 秒...\n');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  console.log('🎯 测试总结:');
  console.log('1. 检查服务器控制台输出，每次调用应该显示不同的地区信息');
  console.log('2. 查找类似 "[REGION] 🎲 Selected random region: XX_XX_city_XX" 的日志');
  console.log('3. 查找类似 "[REGION] 🔐 Built dynamic proxy auth" 的认证构建日志');
  console.log('4. 如果某个地区失败，应该看到自动重试其他地区的日志');
  console.log('5. 所有请求都应该成功返回音频数据');
}

// 启动测试
runTests().catch(error => {
  console.error('测试执行失败:', error);
});
