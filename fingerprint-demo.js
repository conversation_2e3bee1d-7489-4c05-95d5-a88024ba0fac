// fingerprint-demo.js - 浏览器指纹模拟功能演示
// 这个脚本演示新实现的指纹模拟功能

import { 
  getBrowserProfileForSession, 
  getRandomDelay, 
  generateSessionId 
} from './lib/utils.js';

console.log('🎭 浏览器指纹模拟功能演示');
console.log('=====================================\n');

// 演示1: 画像池展示
console.log('📊 1. 可用的浏览器画像:');
console.log('   - Chrome 125 on Windows 11');
console.log('   - Chrome 124 on Windows 10'); 
console.log('   - Firefox 126 on Windows 11');
console.log('   - Safari 17 on macOS Sonoma');
console.log('   - Microsoft Edge 125 on Windows 11\n');

// 演示2: 会话ID生成
console.log('🔑 2. 会话ID生成演示:');
const voiceId1 = 'pNInz6obpgDQGcFmaJgB';
const requestBody1 = '{"text":"Hello world","voice_settings":{"stability":0.5}}';
const sessionId1 = generateSessionId(voiceId1, requestBody1);
console.log(`   Voice ID: ${voiceId1}`);
console.log(`   Request: ${requestBody1.substring(0, 50)}...`);
console.log(`   Session ID: ${sessionId1}\n`);

// 演示3: 画像分配
console.log('🎭 3. 画像分配演示:');
const profile1 = getBrowserProfileForSession(sessionId1);
console.log(`   Session ${sessionId1} -> ${profile1.name}`);

// 同一会话应该得到相同画像
const profile1_again = getBrowserProfileForSession(sessionId1);
console.log(`   Same session again -> ${profile1_again.name} (应该相同)`);

// 不同会话应该可能得到不同画像
const sessionId2 = generateSessionId('different_voice', '{"text":"Different text"}');
const profile2 = getBrowserProfileForSession(sessionId2);
console.log(`   Different session -> ${profile2.name}\n`);

// 演示4: 随机延迟
console.log('⏱️ 4. 随机延迟演示:');
console.log('   正常请求延迟:');
for (let i = 0; i < 5; i++) {
  const delay = getRandomDelay();
  console.log(`     请求 ${i + 1}: ${delay}ms`);
}

console.log('   重试延迟 (指数退避):');
for (let i = 0; i < 3; i++) {
  const retryDelay = getRandomDelay(true, i);
  console.log(`     重试 ${i + 1}: ${retryDelay}ms`);
}

// 演示5: 头部信息展示
console.log('\n📋 5. 浏览器头部信息示例:');
console.log(`   Profile: ${profile1.name}`);
console.log('   Headers:');
Object.entries(profile1.headers).forEach(([key, value]) => {
  console.log(`     ${key}: ${value}`);
});

console.log('\n✅ 指纹模拟功能演示完成!');
console.log('\n🔍 功能特点:');
console.log('   ✅ 多样化的浏览器画像池');
console.log('   ✅ 会话内一致性保证');
console.log('   ✅ 会话间多样性实现');
console.log('   ✅ 智能随机延迟');
console.log('   ✅ 完整的浏览器头部模拟');
console.log('   ✅ 自动会话管理和清理');
