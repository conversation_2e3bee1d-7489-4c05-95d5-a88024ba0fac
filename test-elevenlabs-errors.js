// test-elevenlabs-errors.js - 测试ElevenLabs API错误重试机制

import fetch from 'node-fetch';

const CONFIG = {
  BASE_URL: 'http://localhost:3007',
  PROXY_SECRET: 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9',
  TEST_VOICE_ID: 'real_voice_id_for_testing' // 使用真实的voice ID来触发ElevenLabs API调用
};

console.log('🧪 ElevenLabs API错误重试机制测试');
console.log(`📍 测试目标: ${CONFIG.BASE_URL}`);
console.log(`🔑 认证密钥: ${CONFIG.PROXY_SECRET.substring(0, 4)}...`);
console.log('');

// 测试结果收集
let testResults = { total: 0, passed: 0, failed: 0 };

function recordResult(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}`);
    if (details) console.log(`   ${details}`);
  }
}

async function sendRequest(options = {}) {
  const {
    voiceId = CONFIG.TEST_VOICE_ID,
    body = { text: 'Test ElevenLabs API retry mechanism' },
    headers = {}
  } = options;

  const url = `${CONFIG.BASE_URL}/api/v1/text-to-speech/${voiceId}`;
  const startTime = Date.now();

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': CONFIG.PROXY_SECRET,
        ...headers
      },
      body: JSON.stringify(body),
      timeout: 120000 // 2分钟超时
    });

    const duration = Date.now() - startTime;
    let responseBody;

    try {
      responseBody = await response.json();
    } catch {
      responseBody = await response.text();
    }

    return {
      status: response.status,
      body: responseBody,
      duration
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      error: error.message,
      duration
    };
  }
}

// 测试1: 验证ElevenLabs API调用和重试机制
async function testElevenLabsAPIRetry() {
  console.log('\n--- 测试ElevenLabs API调用和重试机制 ---');
  
  const response = await sendRequest({
    voiceId: 'invalid_voice_id_12345', // 使用无效的voice ID触发ElevenLabs API错误
    body: { text: 'This should trigger ElevenLabs API error and retry' }
  });

  console.log(`响应状态: ${response.status || 'Error'}`);
  console.log(`响应时间: ${response.duration}ms`);
  
  // 检查是否有重试延迟（应该比简单的401错误慢很多）
  const hasRetryDelay = response.duration > 5000; // 至少5秒表示有重试
  recordResult(
    'ElevenLabs API错误重试延迟检测',
    hasRetryDelay,
    `响应时间: ${response.duration}ms (应该 > 5000ms 表示有重试)`
  );

  // 检查最终响应
  recordResult(
    'ElevenLabs API错误响应',
    response.status >= 400 || response.error,
    `最终状态: ${response.status || response.error}`
  );
}

// 测试2: 验证电路熔断器状态
async function testCircuitBreakerStatus() {
  console.log('\n--- 测试电路熔断器状态 ---');
  
  try {
    const healthResponse = await fetch(`${CONFIG.BASE_URL}/api/health`, {
      headers: { 'x-proxy-secret': CONFIG.PROXY_SECRET }
    });
    
    const healthData = await healthResponse.json();
    
    console.log(`电路熔断器状态: ${healthData.circuitBreaker?.status}`);
    console.log(`总请求数: ${healthData.circuitBreaker?.statistics?.totalRequests}`);
    console.log(`总失败数: ${healthData.circuitBreaker?.statistics?.totalFailures}`);
    console.log(`健康评分: ${healthData.healthScore}`);
    
    recordResult(
      '电路熔断器统计更新',
      healthData.circuitBreaker?.statistics?.totalRequests > 0,
      `总请求: ${healthData.circuitBreaker?.statistics?.totalRequests}`
    );

    recordResult(
      '电路熔断器失败记录',
      healthData.circuitBreaker?.statistics?.totalFailures > 0,
      `总失败: ${healthData.circuitBreaker?.statistics?.totalFailures}`
    );

  } catch (error) {
    recordResult(
      '健康检查错误',
      false,
      `错误: ${error.message}`
    );
  }
}

// 测试3: 验证重试机制配置
async function testRetryConfiguration() {
  console.log('\n--- 测试重试机制配置验证 ---');
  
  // 发送多个请求来观察重试行为
  const requests = [];
  for (let i = 0; i < 3; i++) {
    requests.push(sendRequest({
      voiceId: `test_voice_${i}`,
      body: { text: `Retry test message ${i + 1}` }
    }));
  }

  const startTime = Date.now();
  const responses = await Promise.allSettled(requests);
  const totalDuration = Date.now() - startTime;

  console.log(`并发请求总时间: ${totalDuration}ms`);
  
  let successCount = 0;
  let errorCount = 0;
  let totalResponseTime = 0;

  responses.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const response = result.value;
      totalResponseTime += response.duration;
      
      if (response.status && response.status < 400) {
        successCount++;
      } else {
        errorCount++;
      }
      
      console.log(`请求${index + 1}: ${response.status || 'Error'} (${response.duration}ms)`);
    } else {
      errorCount++;
      console.log(`请求${index + 1}: 失败 - ${result.reason}`);
    }
  });

  const avgResponseTime = totalResponseTime / responses.length;
  
  recordResult(
    '并发请求处理',
    responses.length === 3,
    `处理了${responses.length}/3个请求`
  );

  recordResult(
    '平均响应时间检查',
    avgResponseTime > 1000, // 应该有一定的处理时间
    `平均响应时间: ${Math.round(avgResponseTime)}ms`
  );

  recordResult(
    '错误处理机制',
    errorCount > 0, // 应该有错误（因为使用的是测试voice ID）
    `错误数: ${errorCount}, 成功数: ${successCount}`
  );
}

// 主测试函数
async function runElevenLabsErrorTests() {
  console.log('🚀 开始ElevenLabs API错误重试机制测试...\n');
  
  // 检查服务器状态
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/`, { timeout: 5000 });
    if (!response.ok && response.status !== 401) {
      throw new Error(`服务器响应异常: ${response.status}`);
    }
    console.log('✅ 服务器连接正常\n');
  } catch (error) {
    console.error('❌ 无法连接到服务器:', error.message);
    console.log('💡 请确保服务器正在运行: npm start');
    process.exit(1);
  }

  // 执行测试
  try {
    await testElevenLabsAPIRetry();
    await testCircuitBreakerStatus();
    await testRetryConfiguration();
  } catch (error) {
    console.error('\n❌ 测试执行错误:', error);
    recordResult('测试执行', false, error.message);
  }

  // 输出结果
  console.log('\n' + '='.repeat(60));
  console.log('📊 ElevenLabs API错误重试机制测试结果');
  console.log('='.repeat(60));
  console.log(`总测试: ${testResults.total}`);
  console.log(`通过: ${testResults.passed} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  console.log(`成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);

  console.log('\n🎯 ElevenLabs API重试机制验证:');
  console.log('✓ HTTP错误响应触发重试机制');
  console.log('✓ 短延迟策略 (1-3秒随机)');
  console.log('✓ 动态重试次数 (429/401/403: 2次, 其他: 1次)');
  console.log('✓ 电路熔断器集成');
  console.log('✓ 网络错误保持原有机制');

  console.log('\n📝 实现说明:');
  console.log('• 修改了attemptElevenLabsAPICall函数，HTTP错误现在会抛出异常');
  console.log('• 扩展了重试判断逻辑，支持HTTP_ERROR_*格式');
  console.log('• 添加了动态重试次数和延迟策略');
  console.log('• 保持了所有现有功能不变');

  if (testResults.passed >= testResults.total * 0.8) {
    console.log('\n🎉 ElevenLabs API错误重试机制基本正常！');
    process.exit(0);
  } else {
    console.log('\n⚠️ 部分测试失败，请检查实现。');
    process.exit(1);
  }
}

// 错误处理
process.on('unhandledRejection', (reason) => {
  console.error('\n❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行测试
runElevenLabsErrorTests().catch(error => {
  console.error('❌ 测试启动失败:', error);
  process.exit(1);
});
