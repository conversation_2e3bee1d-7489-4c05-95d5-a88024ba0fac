// 日志查看器服务器 - 提供实时日志查看功能
import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import auth from 'basic-auth';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server);

const PORT = process.env.LOG_VIEWER_PORT || 3001;
const LOG_PATH = process.env.LOG_PATH || '/var/www/tts-proxy-vps/logs';
const LOG_PASSWORD = process.env.LOG_PASSWORD || 'logviewer123';

// 基础认证中间件
function requireAuth(req, res, next) {
  const credentials = auth(req);
  
  if (!credentials || credentials.pass !== LOG_PASSWORD) {
    res.set('WWW-Authenticate', 'Basic realm="Log Viewer"');
    return res.status(401).send('Access denied. Please provide correct password.');
  }
  
  next();
}

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 主页面 - 需要认证
app.get('/', requireAuth, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'log-viewer',
    timestamp: new Date().toISOString()
  });
});

// WebSocket连接处理
io.on('connection', (socket) => {
  console.log(`[LOG-VIEWER] Client connected: ${socket.id}`);
  
  // 启动tail进程监控日志文件
  const tailProcesses = {};
  
  // 监控out-0.log (PM2的日志文件格式)
  const outLogPath = path.resolve(__dirname, LOG_PATH, 'out-0.log');
  const tailOut = spawn('tail', ['-f', outLogPath], { stdio: ['ignore', 'pipe', 'pipe'] });
  tailProcesses.out = tailOut;
  
  tailOut.stdout.on('data', (data) => {
    socket.emit('log-data', {
      type: 'out',
      data: data.toString(),
      timestamp: new Date().toISOString()
    });
  });
  
  tailOut.stderr.on('data', (data) => {
    console.error(`[TAIL-OUT] Error: ${data}`);
  });
  
  // 监控error-0.log (PM2的日志文件格式)
  const errorLogPath = path.resolve(__dirname, LOG_PATH, 'error-0.log');
  const tailError = spawn('tail', ['-f', errorLogPath], { stdio: ['ignore', 'pipe', 'pipe'] });
  tailProcesses.error = tailError;
  
  tailError.stdout.on('data', (data) => {
    socket.emit('log-data', {
      type: 'error',
      data: data.toString(),
      timestamp: new Date().toISOString()
    });
  });
  
  tailError.stderr.on('data', (data) => {
    console.error(`[TAIL-ERROR] Error: ${data}`);
  });
  
  // 发送初始日志内容（最后50行）
  const headOut = spawn('tail', ['-n', '50', outLogPath]);
  headOut.stdout.on('data', (data) => {
    socket.emit('log-data', {
      type: 'out',
      data: data.toString(),
      timestamp: new Date().toISOString(),
      initial: true
    });
  });

  headOut.on('error', (err) => {
    console.error(`[HEAD-OUT] Error reading ${outLogPath}:`, err.message);
  });

  const headError = spawn('tail', ['-n', '50', errorLogPath]);
  headError.stdout.on('data', (data) => {
    socket.emit('log-data', {
      type: 'error',
      data: data.toString(),
      timestamp: new Date().toISOString(),
      initial: true
    });
  });

  headError.on('error', (err) => {
    console.error(`[HEAD-ERROR] Error reading ${errorLogPath}:`, err.message);
  });
  
  // 客户端断开连接时清理tail进程
  socket.on('disconnect', () => {
    console.log(`[LOG-VIEWER] Client disconnected: ${socket.id}`);
    
    Object.values(tailProcesses).forEach(process => {
      if (process && !process.killed) {
        process.kill();
      }
    });
  });
  
  // 处理清空日志请求
  socket.on('clear-logs', (data) => {
    socket.emit('logs-cleared', { type: data.type });
  });
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`🔍 Log Viewer is running on http://localhost:${PORT}`);
  console.log(`📁 Monitoring logs in: ${path.resolve(__dirname, LOG_PATH)}`);
  console.log(`🔐 Password: ${LOG_PASSWORD}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('📴 Log Viewer shutting down gracefully...');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('📴 Log Viewer shutting down gracefully...');
  server.close(() => {
    process.exit(0);
  });
});
