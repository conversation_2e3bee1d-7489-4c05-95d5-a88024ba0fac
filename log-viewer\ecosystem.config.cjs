// PM2配置文件 - 日志查看器
module.exports = {
  apps: [{
    name: 'log-viewer',
    script: 'server.js',
    instances: 1,
    exec_mode: 'fork',
    autorestart: true,
    watch: false,
    max_memory_restart: '500M',
    env: {
      NODE_ENV: 'production',
      LOG_VIEWER_PORT: 3001,
      LOG_PATH: '/var/www/tts-proxy-vps/logs',
      LOG_PASSWORD: 'logviewer123'
    },
    env_development: {
      NODE_ENV: 'development',
      LOG_VIEWER_PORT: 3001,
      LOG_PATH: '/var/www/tts-proxy-vps/logs',
      LOG_PASSWORD: 'dev123'
    },
    // 日志配置
    log_file: './logs/log-viewer.log',
    out_file: './logs/log-viewer-out.log',
    error_file: './logs/log-viewer-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // 进程管理配置
    min_uptime: '10s',
    max_restarts: 5,
    restart_delay: 2000
  }]
};
