// test-api-dynamic.js - 测试动态地区功能在实际API调用中的表现

import fetch from 'node-fetch';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const BASE_URL = 'http://localhost:3006';
const PROXY_SECRET = process.env.PROXY_SECRET || 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9';

console.log('🧪 动态地区API测试开始...\n');

// 测试函数
async function testTTSEndpoint(testName, enableDynamicRegions = false) {
  console.log(`📋 ${testName}`);
  console.log(`动态地区功能: ${enableDynamicRegions ? '启用' : '禁用'}`);
  
  // 临时设置环境变量
  const originalValue = process.env.ENABLE_DYNAMIC_REGIONS;
  process.env.ENABLE_DYNAMIC_REGIONS = enableDynamicRegions ? 'true' : 'false';
  
  try {
    const testPayload = {
      text: "Hello, this is a test message for dynamic region proxy testing.",
      model_id: "eleven_monolingual_v1",
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.5
      }
    };

    const response = await fetch(`${BASE_URL}/api/v1/text-to-speech/21m00Tcm4TlvDq8ikWAM`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': PROXY_SECRET
      },
      body: JSON.stringify(testPayload),
      timeout: 30000 // 30秒超时
    });

    console.log(`响应状态: ${response.status} ${response.statusText}`);
    console.log(`Content-Type: ${response.headers.get('Content-Type')}`);
    
    if (response.ok) {
      console.log('✅ API调用成功');
      // 不实际下载音频数据，只检查响应头
      const contentLength = response.headers.get('Content-Length');
      if (contentLength) {
        console.log(`音频数据大小: ${contentLength} bytes`);
      }
    } else {
      console.log('❌ API调用失败');
      const errorText = await response.text();
      console.log('错误信息:', errorText.substring(0, 200));
    }

  } catch (error) {
    console.log('❌ 请求异常:', error.message);
  } finally {
    // 恢复原始环境变量
    if (originalValue !== undefined) {
      process.env.ENABLE_DYNAMIC_REGIONS = originalValue;
    } else {
      delete process.env.ENABLE_DYNAMIC_REGIONS;
    }
  }
  
  console.log('');
}

// 健康检查测试
async function testHealthEndpoint() {
  console.log('📋 健康检查测试');
  
  try {
    const response = await fetch(`${BASE_URL}/api/health`, {
      method: 'GET',
      headers: {
        'x-proxy-secret': PROXY_SECRET
      }
    });

    console.log(`响应状态: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 健康检查成功');
      console.log('响应数据:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ 健康检查失败');
    }

  } catch (error) {
    console.log('❌ 健康检查异常:', error.message);
  }
  
  console.log('');
}

// 执行测试
async function runTests() {
  // 1. 健康检查
  await testHealthEndpoint();
  
  // 2. 静态代理模式测试
  await testTTSEndpoint('测试1: 静态代理模式', false);
  
  // 3. 动态地区模式测试
  await testTTSEndpoint('测试2: 动态地区模式', true);
  
  // 4. 多次动态地区测试（验证地区轮换）
  console.log('📋 测试3: 多次动态地区调用（验证地区轮换）');
  for (let i = 1; i <= 3; i++) {
    console.log(`--- 第 ${i} 次调用 ---`);
    await testTTSEndpoint(`动态地区调用 ${i}`, true);
    
    // 添加延迟避免过快请求
    if (i < 3) {
      console.log('等待 2 秒...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('🎯 测试总结:');
  console.log('1. 如果静态模式和动态模式都成功，说明功能正常');
  console.log('2. 动态模式下，每次调用应该使用不同的地区（查看服务器日志）');
  console.log('3. 如果某个地区失败，系统应该自动重试其他地区');
  console.log('4. 检查服务器控制台输出，观察地区切换和代理认证信息');
}

// 启动测试
runTests().catch(error => {
  console.error('测试执行失败:', error);
});
