# 🔧 日志文件路径修复说明

## 🚨 问题发现

从你的截图可以看出，PM2生成的日志文件名格式是：
- `out.0.log` ✅ (实际存在)
- `error.0.log` ✅ (实际存在)
- `combined.0.log` ✅ (实际存在)

而不是我们之前配置的：
- `out.log` ❌ (不存在)
- `error.log` ❌ (不存在)

## ✅ 已修复的文件

我已经修正了以下文件中的日志文件名：

### 1. server.js
```javascript
// 修改前
const outLogPath = path.resolve(__dirname, LOG_PATH, 'out.log');
const errorLogPath = path.resolve(__dirname, LOG_PATH, 'error.log');

// 修改后
const outLogPath = path.resolve(__dirname, LOG_PATH, 'out.0.log');
const errorLogPath = path.resolve(__dirname, LOG_PATH, 'error.0.log');
```

### 2. public/index.html
```html
<!-- 修改前 -->
<div class="log-title out">📄 输出日志 (out.log)</div>
<div class="log-title error">❌ 错误日志 (error.log)</div>

<!-- 修改后 -->
<div class="log-title out">📄 输出日志 (out.0.log)</div>
<div class="log-title error">❌ 错误日志 (error.0.log)</div>
```

### 3. start-log-viewer.sh
```bash
# 修改前
if [ ! -f "$LOG_PATH/out.log" ]; then
if [ ! -f "$LOG_PATH/error.log" ]; then

# 修改后
if [ ! -f "$LOG_PATH/out.0.log" ]; then
if [ ! -f "$LOG_PATH/error.0.log" ]; then
```

### 4. README.md
更新了所有文档中的文件名引用。

## 🚀 立即修复步骤

在你的服务器上执行以下命令：

```bash
# 1. 停止当前的日志查看器
pm2 stop log-viewer
pm2 delete log-viewer

# 2. 更新代码文件
cd /var/www/log-viewer

# 如果你有Git仓库，拉取最新代码
# git pull

# 或者手动复制修复后的文件
# 复制修复后的文件到服务器

# 3. 重新启动服务
pm2 start ecosystem.config.cjs

# 4. 检查状态
pm2 status log-viewer
pm2 logs log-viewer
```

## 🧪 验证修复

### 1. 检查日志文件是否存在
```bash
ls -la /var/www/tts-proxy-vps/logs/
# 应该看到:
# -rw-r--r-- 1 <USER> <GROUP> size date out.0.log
# -rw-r--r-- 1 <USER> <GROUP> size date error.0.log
```

### 2. 测试tail命令
```bash
# 测试是否能正常读取文件
tail -f /var/www/tts-proxy-vps/logs/out.0.log
tail -f /var/www/tts-proxy-vps/logs/error.0.log
```

### 3. 检查日志查看器日志
```bash
pm2 logs log-viewer
# 应该不再看到 "No such file or directory" 错误
```

### 4. 测试Web界面
```bash
# 访问 http://localhost:3001
# 现在应该能看到实时日志内容了
```

## 📊 PM2日志文件说明

PM2默认的日志文件命名规则：
- `out.0.log` - 标准输出日志
- `error.0.log` - 错误输出日志  
- `combined.0.log` - 合并日志
- 数字后缀表示日志轮转的序号

## 🔄 如果还有问题

### 检查权限
```bash
# 确保日志查看器可以读取日志文件
sudo chmod 644 /var/www/tts-proxy-vps/logs/*.log
```

### 检查文件内容
```bash
# 查看日志文件是否有内容
wc -l /var/www/tts-proxy-vps/logs/out.0.log
wc -l /var/www/tts-proxy-vps/logs/error.0.log
```

### 手动测试
```bash
# 手动测试tail命令
tail -n 10 /var/www/tts-proxy-vps/logs/out.0.log
tail -n 10 /var/www/tts-proxy-vps/logs/error.0.log
```

## ✅ 修复完成后的效果

修复后，你应该能在Web界面看到：
- 实时的TTS代理服务输出日志
- 实时的错误日志（如果有的话）
- 正确的连接状态
- 日志行数统计

现在日志查看器应该能正常工作了！🎉
