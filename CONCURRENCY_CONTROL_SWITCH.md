# 🎛️ 并发控制开关使用说明

## 📋 概述

为了解决代理服务中按voice_id限制并发不合理的问题，我们添加了一个并发控制开关，可以灵活地启用或禁用并发限制功能。

## ⚙️ 配置方式

### **1. 环境变量控制**

#### **禁用并发控制（默认）**
```bash
# 方式1：不设置环境变量（默认禁用）
# 无需任何配置

# 方式2：显式禁用
ENABLE_CONCURRENCY_CONTROL=false
```

#### **启用并发控制**
```bash
# 启用并发控制
ENABLE_CONCURRENCY_CONTROL=true

# 可选：设置每个voice的并发数（默认3）
MAX_CONCURRENT_PER_VOICE=5
```

### **2. 代码中控制**
```javascript
// 创建时指定
const manager = new ConcurrencyManager({
  concurrencyEnabled: true,  // 启用
  maxConcurrentPerVoice: 5
});

// 或者
const manager = new ConcurrencyManager({
  concurrencyEnabled: false  // 禁用
});
```

## 🔍 状态检查

### **1. 启动日志**
```bash
# 禁用时
[CONCURRENCY] 🎛️ Concurrency control: DISABLED
[CONCURRENCY] ⚠️ WARNING: Concurrency limits are disabled - unlimited concurrent requests allowed

# 启用时
[CONCURRENCY] 🎛️ Concurrency control: ENABLED
```

### **2. 运行时日志**
```bash
# 禁用时的请求日志
[CONCURRENCY] ✅ Request allowed for voice_123 (concurrency control disabled)

# 启用时的请求日志
[CONCURRENCY] 🚀 Started req_xxx for voice_123 (1/3)
[CONCURRENCY] 🚫 Request rejected for voice_123 (3/3 active)  # 超限时
```

### **3. 健康检查端点**
```bash
curl -H "x-proxy-secret: YOUR_SECRET" http://localhost:3002/api/health
```

响应中的并发状态：
```json
{
  "concurrency": {
    "status": "idle",
    "enabled": false,
    "configuration": {
      "maxConcurrentPerVoice": 3,
      "concurrencyEnabled": false
    },
    "statistics": {
      "totalRequests": 0,
      "activeRequests": 0,
      "rejectedRequests": 0
    }
  }
}
```

## 🎯 使用场景

### **1. 当前推荐：禁用并发控制**
**原因**：
- 代理服务场景下，按voice_id限制不合理
- 多个用户可能使用相同的热门voice_id
- 会导致不公平的用户体验

**适用情况**：
- 生产环境部署
- 多用户代理服务
- 热门voice_id较多的场景

### **2. 启用并发控制的场景**
**适用情况**：
- 单用户或少量用户的私有部署
- 需要严格控制上游API调用频率
- 测试环境或开发环境
- 已知voice_id使用分布均匀的场景

## 🔧 实际效果对比

### **禁用并发控制**
```bash
# 多个用户同时使用相同voice_id
用户A → voice_popular → ✅ 允许
用户B → voice_popular → ✅ 允许  
用户C → voice_popular → ✅ 允许
用户D → voice_popular → ✅ 允许
# 无限制，公平对待所有用户
```

### **启用并发控制**
```bash
# 多个用户同时使用相同voice_id
用户A → voice_popular → ✅ 允许 (1/3)
用户B → voice_popular → ✅ 允许 (2/3)
用户C → voice_popular → ✅ 允许 (3/3)
用户D → voice_popular → ❌ 拒绝 (429错误)
# 第4个用户被拒绝，体验不佳
```

## 📊 监控和调试

### **1. 关键日志标识**
- `[CONCURRENCY] 🎛️` - 启动时的状态信息
- `[CONCURRENCY] ✅` - 请求允许（禁用时）
- `[CONCURRENCY] 🚀` - 请求开始（启用时）
- `[CONCURRENCY] 🚫` - 请求拒绝（启用时）
- `[CONCURRENCY] ✅ Finished` - 请求完成

### **2. 统计信息**
通过健康检查端点可以获取：
- `totalRequests` - 总请求数
- `activeRequests` - 当前活跃请求数
- `rejectedRequests` - 被拒绝的请求数
- `activeByVoice` - 每个voice的活跃请求数

## 🚨 注意事项

### **1. 禁用并发控制的风险**
- **上游API压力**: 可能对ElevenLabs API造成过大压力
- **资源消耗**: 服务器内存和CPU使用可能增加
- **需要监控**: 建议密切监控系统资源和响应时间

### **2. 启用并发控制的问题**
- **用户体验**: 热门voice用户可能频繁遇到429错误
- **不公平性**: 不同voice的用户体验差异很大
- **业务影响**: 可能影响付费用户的使用体验

## 🔄 切换方法

### **运行时切换（需要重启）**
```bash
# 当前禁用，想要启用
export ENABLE_CONCURRENCY_CONTROL=true
pm2 restart tts-proxy

# 当前启用，想要禁用
export ENABLE_CONCURRENCY_CONTROL=false
pm2 restart tts-proxy
```

### **配置文件切换**
```bash
# 修改 .env 文件
echo "ENABLE_CONCURRENCY_CONTROL=true" >> .env
pm2 restart tts-proxy
```

## 📈 性能建议

### **当前推荐配置（生产环境）**
```bash
# .env 文件
ENABLE_CONCURRENCY_CONTROL=false  # 禁用并发控制
# MAX_CONCURRENT_PER_VOICE=3      # 禁用时此参数无效
```

### **监控指标**
建议监控以下指标来评估效果：
- 响应时间分布
- 错误率（特别是429错误）
- 服务器资源使用率
- 用户投诉和反馈

## 🎯 总结

**当前状态**: 并发控制已禁用，所有请求都会被允许，不再有voice_id级别的并发限制。

**优势**: 
- ✅ 解决了热门voice_id的用户体验问题
- ✅ 提供了公平的服务访问
- ✅ 简化了并发管理逻辑

**注意**: 
- ⚠️ 需要监控系统资源使用情况
- ⚠️ 可能需要在上游或网关层面实施其他限流策略
- ⚠️ 保留了开关机制，可以根据实际情况随时调整
