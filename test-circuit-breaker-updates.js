#!/usr/bin/env node

// 测试熔断器更新功能
import { elevenLabsCircuitBreaker } from './lib/utils.js';

console.log('🔍 测试熔断器更新功能...\n');

// 测试1: 验证配置更新
console.log('📋 测试1: 验证配置更新');
console.log(`失败阈值: ${elevenLabsCircuitBreaker.failureThreshold} (应该是8)`);
console.log(`重置超时: ${elevenLabsCircuitBreaker.resetTimeout}ms (应该是30000)`);
console.log(`当前状态: ${elevenLabsCircuitBreaker.state} (应该是CLOSED)`);
console.log(`健康状态: ${elevenLabsCircuitBreaker.isHealthy()} (应该是true)`);

const configCorrect = elevenLabsCircuitBreaker.failureThreshold === 8 && 
                     elevenLabsCircuitBreaker.resetTimeout === 30000 &&
                     elevenLabsCircuitBreaker.state === 'CLOSED' &&
                     elevenLabsCircuitBreaker.isHealthy() === true;

console.log(`✅ 配置更新测试: ${configCorrect ? '通过' : '失败'}\n`);

// 测试2: 验证内容违规错误检测
console.log('📋 测试2: 验证内容违规错误检测');

const contentPolicyError = new Error('HTTP_ERROR_403: {"detail":{"status":"content_against_policy","message":"We are sorry but text you are trying to use may violate our Terms of Service and has been blocked."}}');
const normalError = new Error('HTTP_ERROR_403: {"detail":{"status":"unauthorized","message":"Invalid API key"}}');
const networkError = new Error('ECONNREFUSED: Connection refused');

const test1 = elevenLabsCircuitBreaker._isContentPolicyError(contentPolicyError);
const test2 = elevenLabsCircuitBreaker._isContentPolicyError(normalError);
const test3 = elevenLabsCircuitBreaker._isContentPolicyError(networkError);

console.log(`内容违规错误检测: ${test1} (应该是true)`);
console.log(`普通403错误检测: ${test2} (应该是false)`);
console.log(`网络错误检测: ${test3} (应该是false)`);

const errorDetectionCorrect = test1 === true && test2 === false && test3 === false;
console.log(`✅ 错误检测测试: ${errorDetectionCorrect ? '通过' : '失败'}\n`);

// 测试3: 验证熔断器行为（模拟）
console.log('📋 测试3: 验证熔断器行为');

let testResults = [];

// 模拟内容违规错误（不应该触发熔断）
try {
  await elevenLabsCircuitBreaker.execute(async () => {
    throw contentPolicyError;
  });
} catch (error) {
  const stillHealthy = elevenLabsCircuitBreaker.isHealthy();
  const failureCount = elevenLabsCircuitBreaker.failures;
  testResults.push({
    test: '内容违规错误不触发熔断',
    passed: stillHealthy && failureCount === 0,
    details: `健康状态: ${stillHealthy}, 失败计数: ${failureCount}`
  });
}

// 模拟普通错误（应该增加失败计数）
try {
  await elevenLabsCircuitBreaker.execute(async () => {
    throw normalError;
  });
} catch (error) {
  const stillHealthy = elevenLabsCircuitBreaker.isHealthy();
  const failureCount = elevenLabsCircuitBreaker.failures;
  testResults.push({
    test: '普通错误增加失败计数',
    passed: stillHealthy && failureCount === 1,
    details: `健康状态: ${stillHealthy}, 失败计数: ${failureCount}`
  });
}

// 模拟成功请求（应该重置失败计数）
try {
  await elevenLabsCircuitBreaker.execute(async () => {
    return 'success';
  });
  const stillHealthy = elevenLabsCircuitBreaker.isHealthy();
  const failureCount = elevenLabsCircuitBreaker.failures;
  testResults.push({
    test: '成功请求重置失败计数',
    passed: stillHealthy && failureCount === 0,
    details: `健康状态: ${stillHealthy}, 失败计数: ${failureCount}`
  });
} catch (error) {
  testResults.push({
    test: '成功请求重置失败计数',
    passed: false,
    details: `意外错误: ${error.message}`
  });
}

// 输出测试结果
testResults.forEach(result => {
  console.log(`${result.passed ? '✅' : '❌'} ${result.test}: ${result.details}`);
});

const behaviorTestsPassed = testResults.every(r => r.passed);
console.log(`✅ 行为测试: ${behaviorTestsPassed ? '通过' : '失败'}\n`);

// 总结
console.log('📊 测试总结:');
console.log(`配置更新: ${configCorrect ? '✅ 通过' : '❌ 失败'}`);
console.log(`错误检测: ${errorDetectionCorrect ? '✅ 通过' : '❌ 失败'}`);
console.log(`行为验证: ${behaviorTestsPassed ? '✅ 通过' : '❌ 失败'}`);

const allTestsPassed = configCorrect && errorDetectionCorrect && behaviorTestsPassed;
console.log(`\n🎉 总体结果: ${allTestsPassed ? '所有测试通过！' : '部分测试失败'}`);

if (allTestsPassed) {
  console.log('\n✨ 熔断器更新功能验证完成：');
  console.log('• 失败阈值已调高到8次');
  console.log('• 移除了HALF_OPEN状态，简化为两状态模式');
  console.log('• 内容违规错误不触发熔断和重试');
  console.log('• 其他逻辑保持不变');
}
