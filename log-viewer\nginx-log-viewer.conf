# Nginx配置 - 日志查看器
# 将此文件复制到 /etc/nginx/sites-available/log-viewer
# 然后创建软链接: sudo ln -s /etc/nginx/sites-available/log-viewer /etc/nginx/sites-enabled/

server {
    listen 80;
    listen [::]:80;
    server_name logs.tts-proxy-ca.aispeak.top;  # 修改为你的子域名

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # 限制访问（可选 - 只允许特定IP访问）
    # allow ***********/24;  # 允许内网访问
    # allow YOUR_IP_ADDRESS;  # 允许你的IP访问
    # deny all;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # WebSocket支持
        proxy_set_header Connection "upgrade";
        proxy_set_header Upgrade $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Socket.IO路径
    location /socket.io/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 访问日志
    access_log /var/log/nginx/log-viewer.access.log;
    error_log /var/log/nginx/log-viewer.error.log;
}

# HTTPS配置（使用certbot自动配置）
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name logs.tts-proxy-ca.aispeak.top;
#
#     ssl_certificate /etc/letsencrypt/live/logs.tts-proxy-ca.aispeak.top/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/logs.tts-proxy-ca.aispeak.top/privkey.pem;
#     include /etc/letsencrypt/options-ssl-nginx.conf;
#     ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
#
#     # 其他配置与HTTP版本相同...
# }
