# 🔍 TTS代理服务 - 实时日志查看器

一个简单而强大的Web日志查看器，用于实时监控TTS代理服务的日志文件。

## ✨ 功能特性

- 🔄 **实时日志流**：使用WebSocket实时推送日志内容
- 📊 **双面板显示**：同时显示out.log和error.log
- 🔐 **基础认证**：简单的密码保护
- 🎨 **暗色主题**：适合长时间查看的护眼界面
- 📱 **响应式设计**：支持桌面和移动设备
- 🧹 **日志管理**：支持清空日志显示
- ⚡ **自动滚动**：可切换的自动滚动到最新日志

## 🚀 快速部署

### 1. 安装依赖

```bash
cd Ubuntu服务器版本/log-viewer
npm install
```

### 2. 配置环境变量

```bash
# 方法1: 直接设置
export LOG_PASSWORD="your_secure_password"
export LOG_VIEWER_PORT=3001
export LOG_PATH="/var/www/tts-proxy-vps/logs"

# 方法2: 修改ecosystem.config.cjs
# 编辑文件中的env部分
```

### 3. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
pm2 start ecosystem.config.cjs

# 查看状态
pm2 status log-viewer
pm2 logs log-viewer
```

### 4. 配置Nginx（可选但推荐）

```bash
# 复制Nginx配置
sudo cp nginx-log-viewer.conf /etc/nginx/sites-available/log-viewer

# 修改域名
sudo nano /etc/nginx/sites-available/log-viewer
# 将 logs.tts-proxy-ca.aispeak.top 改为你的域名

# 启用站点
sudo ln -s /etc/nginx/sites-available/log-viewer /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 5. 配置HTTPS（推荐）

```bash
# 获取SSL证书
sudo certbot --nginx -d logs.your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `LOG_VIEWER_PORT` | 3001 | 日志查看器端口 |
| `LOG_PATH` | /var/www/tts-proxy-vps/logs | 日志文件路径 |
| `LOG_PASSWORD` | logviewer123 | 访问密码 |
| `NODE_ENV` | development | 运行环境 |

### 日志文件路径

默认监控以下PM2日志文件：
- `/var/www/tts-proxy-vps/logs/out.0.log` - TTS代理服务输出日志
- `/var/www/tts-proxy-vps/logs/error.0.log` - TTS代理服务错误日志

## 🌐 访问方式

### 本地访问
```
http://localhost:3001
```

### 通过域名访问（配置Nginx后）
```
http://logs.your-domain.com
https://logs.your-domain.com  # 配置SSL后
```

### 认证信息
- 用户名：任意（留空即可）
- 密码：环境变量`LOG_PASSWORD`设置的值

## 📱 使用说明

### 界面功能

1. **连接状态**：顶部显示WebSocket连接状态
2. **自动滚动**：可以开启/关闭自动滚动到最新日志
3. **清空日志**：可以清空显示的日志内容（不影响实际文件）
4. **实时计数**：显示当前显示的日志行数
5. **时间戳**：每行日志都有时间戳

### 快捷操作

- **自动滚动切换**：点击"自动滚动"按钮
- **清空单个日志**：点击对应的"清空"按钮
- **清空所有日志**：点击"清空所有日志"按钮

## 🔒 安全建议

1. **修改默认密码**：
```bash
export LOG_PASSWORD="your_very_secure_password_here"
```

2. **限制IP访问**（在Nginx配置中）：
```nginx
allow ***********/24;  # 允许内网
allow YOUR_IP_ADDRESS;  # 允许特定IP
deny all;              # 拒绝其他所有IP
```

3. **使用HTTPS**：
```bash
sudo certbot --nginx -d logs.your-domain.com
```

4. **防火墙配置**：
```bash
# 只允许通过Nginx访问，不直接暴露3001端口
sudo ufw deny 3001
sudo ufw allow 'Nginx Full'
```

## 🛠️ 故障排除

### 常见问题

1. **无法连接WebSocket**
```bash
# 检查服务状态
pm2 status log-viewer
pm2 logs log-viewer

# 检查端口占用
sudo lsof -i :3001
```

2. **日志文件不存在**
```bash
# 检查日志路径
ls -la ../logs/
# 确保TTS代理服务已启动并生成日志
```

3. **权限问题**
```bash
# 确保日志查看器有读取日志文件的权限
sudo chmod 644 ../logs/*.log
```

4. **Nginx配置问题**
```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

## 📊 性能说明

- **内存使用**：约50-100MB
- **CPU使用**：很低，主要在有新日志时
- **网络带宽**：取决于日志产生速度
- **并发连接**：支持多个客户端同时查看

## 🔄 更新和维护

```bash
# 重启服务
pm2 restart log-viewer

# 更新代码后重新加载
pm2 reload log-viewer

# 查看详细状态
pm2 show log-viewer

# 监控资源使用
pm2 monit
```

## 📝 日志轮转

建议配置日志轮转以防止日志文件过大：

```bash
# 创建logrotate配置
sudo nano /etc/logrotate.d/tts-proxy-logs

# 内容：
/var/www/tts-proxy-vps/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    copytruncate
}
```

现在你就有了一个功能完整的实时日志查看器！🎉
