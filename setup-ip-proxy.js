#!/usr/bin/env node

// 🔧 IP代理快速配置脚本
// 使用方法: node setup-ip-proxy.js

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🌐 IP代理配置助手');
console.log('================');

// 检查是否提供了命令行参数
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log('📋 使用方法:');
  console.log('node setup-ip-proxy.js <IP> <端口> <用户名> <密码>');
  console.log('');
  console.log('📝 示例:');
  console.log('node setup-ip-proxy.js ************* 8080 myuser mypass');
  console.log('');
  console.log('🔧 或者手动编辑以下文件:');
  console.log('- .env (开发环境)');
  console.log('- ecosystem.config.cjs (生产环境)');
  process.exit(1);
}

if (args.length !== 4) {
  console.error('❌ 错误: 需要提供4个参数 (IP, 端口, 用户名, 密码)');
  process.exit(1);
}

const [proxyIP, proxyPort, proxyUsername, proxyPassword] = args;

console.log('📊 配置信息:');
console.log(`   代理IP: ${proxyIP}`);
console.log(`   端口: ${proxyPort}`);
console.log(`   用户名: ${proxyUsername}`);
console.log(`   密码: ${proxyPassword.substring(0, 3)}***`);
console.log('');

// 1. 更新 .env 文件
const envPath = path.join(__dirname, '.env');
const envContent = `# 🔧 IP代理配置
PROXY_IP=${proxyIP}
PROXY_PORT=${proxyPort}
PROXY_USERNAME=${proxyUsername}
PROXY_PASSWORD=${proxyPassword}

# 【应用配置】
NODE_ENV=production
PORT=3002

# 【安全配置】
PROXY_SECRET=AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9

# 配置时间: ${new Date().toISOString()}
`;

try {
  fs.writeFileSync(envPath, envContent, 'utf8');
  console.log('✅ .env 文件已更新');
} catch (error) {
  console.error('❌ 更新 .env 文件失败:', error.message);
}

// 2. PM2配置说明
console.log('📋 PM2配置说明:');
console.log('   当前PM2配置已优化为从.env文件读取代理配置');
console.log('   无需手动修改ecosystem.config.cjs文件');
console.log('   所有代理配置统一通过.env文件管理');

console.log('');
console.log('🎉 配置完成!');
console.log('');
console.log('📋 下一步:');
console.log('1. 重启服务器以应用新配置:');
console.log('   npm start (开发环境)');
console.log('   或');
console.log('   pm2 restart ubuntu-tts-proxy-ip (生产环境)');
console.log('');
console.log('2. 检查日志确认IP代理正常工作:');
console.log('   应该看到: [PROXY] ✅ 初始化IP代理配置');
console.log('   应该看到: [PROXY] 🌐 代理服务器: 你的代理地址');
console.log('');
console.log('3. 测试API功能:');
console.log('   npm test');
console.log('');
console.log('🔒 安全提示:');
console.log('- 生产环境建议使用更强的PROXY_SECRET');
console.log('- 定期更换代理密码');
console.log('- 不要将包含密码的配置文件提交到代码仓库');
