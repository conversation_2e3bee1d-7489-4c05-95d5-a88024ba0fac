<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS代理服务 - 实时日志查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background-color: #1a1a1a;
            color: #e0e0e0;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header .subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .controls {
            background-color: #2a2a2a;
            padding: 10px 20px;
            border-bottom: 1px solid #444;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .btn.danger {
            background-color: #e74c3c;
        }

        .btn.danger:hover {
            background-color: #c0392b;
        }

        .container {
            display: flex;
            height: calc(100vh - 120px);
        }

        .log-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #444;
        }

        .log-panel:last-child {
            border-right: none;
        }

        .log-header {
            background-color: #333;
            padding: 10px 15px;
            border-bottom: 1px solid #444;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-title {
            font-weight: bold;
            font-size: 14px;
        }

        .log-title.out {
            color: #27ae60;
        }

        .log-title.error {
            color: #e74c3c;
        }

        .log-content {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            background-color: #1e1e1e;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-line {
            margin-bottom: 2px;
            padding: 2px 5px;
            border-radius: 2px;
            word-wrap: break-word;
        }

        .log-line.out {
            border-left: 3px solid #27ae60;
            background-color: rgba(39, 174, 96, 0.1);
        }

        .log-line.error {
            border-left: 3px solid #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
        }

        .log-line.initial {
            opacity: 0.7;
        }

        .timestamp {
            color: #888;
            font-size: 10px;
            margin-right: 8px;
        }

        .log-text {
            color: #e0e0e0;
        }

        .log-line.error .log-text {
            color: #ff6b6b;
        }

        .log-line.out .log-text {
            color: #51cf66;
        }

        .footer {
            background-color: #2a2a2a;
            padding: 8px 20px;
            border-top: 1px solid #444;
            font-size: 11px;
            color: #888;
            text-align: center;
        }

        /* 滚动条样式 */
        .log-content::-webkit-scrollbar {
            width: 8px;
        }

        .log-content::-webkit-scrollbar-track {
            background: #2a2a2a;
        }

        .log-content::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }

        .log-content::-webkit-scrollbar-thumb:hover {
            background: #777;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 TTS代理服务 - 实时日志查看器</h1>
        <div class="subtitle">监控 /var/www/tts-proxy-vps/logs 目录下的PM2日志文件</div>
    </div>

    <div class="controls">
        <div class="status">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">连接中...</span>
        </div>
        <button class="btn" onclick="toggleAutoScroll()">
            <span id="autoScrollText">自动滚动: 开</span>
        </button>
        <button class="btn danger" onclick="clearLogs('out')">清空输出日志</button>
        <button class="btn danger" onclick="clearLogs('error')">清空错误日志</button>
        <button class="btn danger" onclick="clearAllLogs()">清空所有日志</button>
    </div>

    <div class="container">
        <div class="log-panel">
            <div class="log-header">
                <div class="log-title out">📄 输出日志 (out-0.log)</div>
                <div id="outCount">0 行</div>
            </div>
            <div class="log-content" id="outLogs"></div>
        </div>

        <div class="log-panel">
            <div class="log-header">
                <div class="log-title error">❌ 错误日志 (error-0.log)</div>
                <div id="errorCount">0 行</div>
            </div>
            <div class="log-content" id="errorLogs"></div>
        </div>
    </div>

    <div class="footer">
        实时日志查看器 | 最后更新: <span id="lastUpdate">--</span> | 连接状态: <span id="connectionStatus">断开</span>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        const socket = io();
        let autoScroll = true;
        let outLogCount = 0;
        let errorLogCount = 0;

        const outLogsContainer = document.getElementById('outLogs');
        const errorLogsContainer = document.getElementById('errorLogs');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        const lastUpdate = document.getElementById('lastUpdate');
        const connectionStatus = document.getElementById('connectionStatus');
        const outCount = document.getElementById('outCount');
        const errorCount = document.getElementById('errorCount');

        // 连接状态处理
        socket.on('connect', () => {
            statusDot.style.backgroundColor = '#27ae60';
            statusText.textContent = '已连接';
            connectionStatus.textContent = '已连接';
            console.log('Connected to log viewer server');
        });

        socket.on('disconnect', () => {
            statusDot.style.backgroundColor = '#e74c3c';
            statusText.textContent = '连接断开';
            connectionStatus.textContent = '断开';
            console.log('Disconnected from log viewer server');
        });

        // 接收日志数据
        socket.on('log-data', (data) => {
            const container = data.type === 'out' ? outLogsContainer : errorLogsContainer;
            const lines = data.data.trim().split('\n').filter(line => line.length > 0);
            
            lines.forEach(line => {
                const logLine = document.createElement('div');
                logLine.className = `log-line ${data.type} ${data.initial ? 'initial' : ''}`;
                
                const timestamp = document.createElement('span');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date(data.timestamp).toLocaleTimeString();
                
                const logText = document.createElement('span');
                logText.className = 'log-text';
                logText.textContent = line;
                
                logLine.appendChild(timestamp);
                logLine.appendChild(logText);
                container.appendChild(logLine);
                
                // 更新计数
                if (data.type === 'out') {
                    outLogCount++;
                    outCount.textContent = `${outLogCount} 行`;
                } else {
                    errorLogCount++;
                    errorCount.textContent = `${errorLogCount} 行`;
                }
            });

            // 限制日志行数（保持最新1000行）
            while (container.children.length > 1000) {
                container.removeChild(container.firstChild);
            }

            // 自动滚动到底部
            if (autoScroll) {
                container.scrollTop = container.scrollHeight;
            }

            // 更新最后更新时间
            lastUpdate.textContent = new Date().toLocaleTimeString();
        });

        // 清空日志响应
        socket.on('logs-cleared', (data) => {
            if (data.type === 'out') {
                outLogsContainer.innerHTML = '';
                outLogCount = 0;
                outCount.textContent = '0 行';
            } else if (data.type === 'error') {
                errorLogsContainer.innerHTML = '';
                errorLogCount = 0;
                errorCount.textContent = '0 行';
            }
        });

        // 切换自动滚动
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            document.getElementById('autoScrollText').textContent = `自动滚动: ${autoScroll ? '开' : '关'}`;
        }

        // 清空日志
        function clearLogs(type) {
            if (confirm(`确定要清空${type === 'out' ? '输出' : '错误'}日志吗？`)) {
                socket.emit('clear-logs', { type });
            }
        }

        function clearAllLogs() {
            if (confirm('确定要清空所有日志吗？')) {
                socket.emit('clear-logs', { type: 'out' });
                socket.emit('clear-logs', { type: 'error' });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Log viewer initialized');
        });
    </script>
</body>
</html>
