# 📁 部署路径配置说明

## 🎯 正确的部署路径

根据你的服务器配置，以下是正确的路径设置：

### TTS代理服务
```
部署路径: /var/www/tts-proxy-vps/
日志路径: /var/www/tts-proxy-vps/logs/
```

### 日志查看器
```
部署路径: /var/www/log-viewer/
监控路径: /var/www/tts-proxy-vps/logs/
```

## 🚀 部署步骤

### 1. 复制日志查看器到服务器

```bash
# 在服务器上创建目录
sudo mkdir -p /var/www/log-viewer
sudo chown $USER:$USER /var/www/log-viewer

# 复制文件到服务器
# 方法1: 如果在本地，使用scp
scp -r Ubuntu服务器版本/log-viewer/* user@your-server:/var/www/log-viewer/

# 方法2: 如果已经在服务器上
cp -r Ubuntu服务器版本/log-viewer/* /var/www/log-viewer/
```

### 2. 设置正确的权限

```bash
cd /var/www/log-viewer

# 设置文件权限
chmod 644 *.js *.json *.md *.conf
chmod 755 start-log-viewer.sh
chmod 644 public/*

# 确保可以读取日志文件
sudo chmod 644 /var/www/tts-proxy-vps/logs/*.log
```

### 3. 安装依赖并启动

```bash
cd /var/www/log-viewer

# 安装依赖
npm install

# 启动服务
./start-log-viewer.sh
```

## 🔧 路径验证

### 检查日志文件是否存在

```bash
# 检查TTS代理日志文件
ls -la /var/www/tts-proxy-vps/logs/
# 应该看到:
# -rw-r--r-- 1 <USER> <GROUP>  size date out.log
# -rw-r--r-- 1 <USER> <GROUP>  size date error.log

# 检查日志查看器文件
ls -la /var/www/log-viewer/
# 应该看到:
# -rw-r--r-- 1 <USER> <GROUP>  size date server.js
# -rw-r--r-- 1 <USER> <GROUP>  size date package.json
# drwxr-xr-x 2 <USER> <GROUP>  size date public/
```

### 测试路径访问

```bash
# 测试日志查看器是否能读取日志文件
cd /var/www/log-viewer
node -e "
const fs = require('fs');
const path = '/var/www/tts-proxy-vps/logs/out.log';
try {
  const content = fs.readFileSync(path, 'utf8');
  console.log('✅ 可以读取out.log');
  console.log('最后几行:', content.split('\n').slice(-3).join('\n'));
} catch (err) {
  console.log('❌ 无法读取out.log:', err.message);
}
"
```

## 🔒 权限问题解决

如果遇到权限问题：

```bash
# 确保日志查看器用户可以读取日志文件
sudo chmod 644 /var/www/tts-proxy-vps/logs/*.log

# 或者将日志查看器用户添加到TTS代理服务的用户组
sudo usermod -a -G $(stat -c '%G' /var/www/tts-proxy-vps/logs) $USER

# 重新登录以应用组权限
```

## 📊 环境变量确认

确认以下环境变量设置正确：

```bash
# 在 /var/www/log-viewer/ecosystem.config.cjs 中
LOG_PATH: '/var/www/tts-proxy-vps/logs'

# 或者通过环境变量设置
export LOG_PATH="/var/www/tts-proxy-vps/logs"
```

## 🧪 功能测试

```bash
# 1. 启动日志查看器
cd /var/www/log-viewer
pm2 start ecosystem.config.cjs

# 2. 检查服务状态
pm2 status log-viewer

# 3. 测试健康检查
curl http://localhost:3001/health

# 4. 查看日志查看器的日志
pm2 logs log-viewer

# 5. 测试Web界面
# 在浏览器中访问: http://your-server-ip:3001
# 用户名: 任意
# 密码: logviewer123
```

## 🌐 Nginx配置更新

如果使用Nginx，确保配置文件中的路径正确：

```bash
# 编辑Nginx配置
sudo nano /etc/nginx/sites-available/log-viewer

# 确认proxy_pass指向正确的端口
proxy_pass http://localhost:3001;
```

## 📝 故障排除

### 常见问题

1. **无法读取日志文件**
```bash
# 检查文件是否存在
ls -la /var/www/tts-proxy-vps/logs/

# 检查权限
stat /var/www/tts-proxy-vps/logs/out.log
```

2. **日志查看器启动失败**
```bash
# 查看详细错误
pm2 logs log-viewer --lines 50

# 检查端口占用
sudo lsof -i :3001
```

3. **WebSocket连接失败**
```bash
# 检查防火墙
sudo ufw status

# 确保端口开放
sudo ufw allow 3001
```

## ✅ 部署完成检查清单

- [ ] 日志查看器文件已复制到 `/var/www/log-viewer/`
- [ ] 依赖已安装 (`npm install`)
- [ ] 权限设置正确
- [ ] 可以读取 `/var/www/tts-proxy-vps/logs/out.log`
- [ ] 可以读取 `/var/www/tts-proxy-vps/logs/error.log`
- [ ] PM2服务启动成功
- [ ] 健康检查通过
- [ ] Web界面可以访问
- [ ] 实时日志显示正常

现在路径配置已经正确，可以正常监控TTS代理服务的日志了！🎉
