// test-retry-simple.js - 简化版重试机制测试（避免编码问题）

import fetch from 'node-fetch';

const CONFIG = {
  BASE_URL: 'http://localhost:3007',
  PROXY_SECRET: 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9',
  TEST_VOICE_ID: 'test_voice_id'
};

console.log('=== Retry Mechanism Tests ===');
console.log('Target:', CONFIG.BASE_URL);
console.log('Secret:', CONFIG.PROXY_SECRET.substring(0, 4) + '...');
console.log('');

let testResults = { total: 0, passed: 0, failed: 0 };

function recordResult(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}`);
    if (details) console.log(`   ${details}`);
  }
}

async function sendRequest(options = {}) {
  const {
    voiceId = CONFIG.TEST_VOICE_ID,
    body = { text: 'Test message' },
    headers = {}
  } = options;

  const url = `${CONFIG.BASE_URL}/api/v1/text-to-speech/${voiceId}`;
  const startTime = Date.now();

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': CONFIG.PROXY_SECRET,
        ...headers
      },
      body: JSON.stringify(body),
      timeout: 10000
    });

    const duration = Date.now() - startTime;
    let responseBody;

    try {
      responseBody = await response.json();
    } catch {
      responseBody = await response.text();
    }

    return {
      status: response.status,
      body: responseBody,
      duration
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      error: error.message,
      duration
    };
  }
}

// Test 1: 401 Unauthorized - Should NOT retry
async function test401Unauthorized() {
  console.log('\n--- Test 1: 401 Unauthorized ---');
  
  const response = await sendRequest({
    headers: { 'x-proxy-secret': 'invalid_secret' }
  });

  recordResult(
    '401 Status Code',
    response.status === 401,
    `Expected 401, got ${response.status}`
  );

  recordResult(
    '401 Fast Response (No Retry)',
    response.duration < 2000,
    `Duration: ${response.duration}ms (should be < 2000ms)`
  );

  recordResult(
    '401 Error Message',
    response.body && (response.body.error || response.body.message),
    'Should contain error information'
  );
}

// Test 2: Network Error - Should retry
async function testNetworkError() {
  console.log('\n--- Test 2: Network Error ---');

  const startTime = Date.now();
  try {
    await fetch('http://localhost:99999/nonexistent', {
      method: 'POST',
      timeout: 3000
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    
    recordResult(
      'Network Error Detection',
      error.code === 'ECONNREFUSED' || error.message.includes('ECONNREFUSED'),
      `Error: ${error.code || error.name}`
    );

    recordResult(
      'Network Error Timeout',
      duration >= 2000,
      `Duration: ${duration}ms (should attempt connection)`
    );
  }
}

// Test 3: Circuit Breaker Status
async function testCircuitBreakerStatus() {
  console.log('\n--- Test 3: Circuit Breaker Status ---');
  
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/api/health`, {
      headers: { 'x-proxy-secret': CONFIG.PROXY_SECRET }
    });
    
    const healthData = await response.json();
    
    recordResult(
      'Health Check Accessible',
      response.status === 200,
      `Status: ${response.status}`
    );

    recordResult(
      'Circuit Breaker Info Present',
      healthData.circuitBreaker && healthData.circuitBreaker.status,
      `CB Status: ${healthData.circuitBreaker?.status}`
    );

    recordResult(
      'Circuit Breaker Healthy',
      healthData.circuitBreaker?.isHealthy === true,
      `Is Healthy: ${healthData.circuitBreaker?.isHealthy}`
    );

    recordResult(
      'Statistics Available',
      healthData.circuitBreaker?.statistics &&
      typeof healthData.circuitBreaker.statistics.totalRequests === 'number',
      `Total Requests: ${healthData.circuitBreaker?.statistics?.totalRequests}`
    );

  } catch (error) {
    recordResult(
      'Health Check Error',
      false,
      `Error: ${error.message}`
    );
  }
}

// Test 4: Concurrent Requests
async function testConcurrentRequests() {
  console.log('\n--- Test 4: Concurrent Requests ---');
  
  const requests = [];
  for (let i = 0; i < 3; i++) {
    requests.push(sendRequest({
      body: { text: `Concurrent test ${i + 1}` }
    }));
  }

  const startTime = Date.now();
  const responses = await Promise.allSettled(requests);
  const duration = Date.now() - startTime;

  recordResult(
    'Concurrent Requests Completed',
    responses.length === 3,
    `Completed: ${responses.length}/3`
  );

  const successCount = responses.filter(r => 
    r.status === 'fulfilled' && r.value.status
  ).length;

  recordResult(
    'Concurrent Responses Received',
    successCount > 0,
    `Successful responses: ${successCount}/3`
  );

  recordResult(
    'Concurrent Processing Time',
    duration < 30000,
    `Duration: ${duration}ms`
  );
}

// Test 5: Error Response Format
async function testErrorResponseFormat() {
  console.log('\n--- Test 5: Error Response Format ---');
  
  // Test with empty body
  const emptyResponse = await sendRequest({
    body: {}
  });

  recordResult(
    'Empty Body Handling',
    emptyResponse.status >= 400,
    `Status: ${emptyResponse.status}`
  );

  // Test with invalid voice ID
  const invalidVoiceResponse = await sendRequest({
    voiceId: 'invalid_voice_12345'
  });

  recordResult(
    'Invalid Voice ID Handling',
    invalidVoiceResponse.status >= 400,
    `Status: ${invalidVoiceResponse.status}`
  );

  recordResult(
    'Error Response Format',
    invalidVoiceResponse.body && 
    (invalidVoiceResponse.body.error || invalidVoiceResponse.body.message),
    'Should contain error information'
  );
}

// Main test execution
async function runTests() {
  console.log('Starting retry mechanism tests...\n');
  
  // Check server status
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/`, { timeout: 5000 });
    if (!response.ok && response.status !== 401) {
      throw new Error(`Server error: ${response.status}`);
    }
    console.log('✅ Server is accessible\n');
  } catch (error) {
    console.error('❌ Server not accessible:', error.message);
    console.log('Please start the server: npm start');
    process.exit(1);
  }

  // Run all tests
  try {
    await test401Unauthorized();
    await testNetworkError();
    await testCircuitBreakerStatus();
    await testConcurrentRequests();
    await testErrorResponseFormat();
  } catch (error) {
    console.error('\nTest execution error:', error);
    recordResult('Test Execution', false, error.message);
  }

  // Print results
  console.log('\n' + '='.repeat(50));
  console.log('TEST RESULTS SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${Math.round((testResults.passed / testResults.total) * 100)}%`);

  console.log('\nKey Retry Mechanism Validations:');
  console.log('✓ 401 errors do not retry (fast response)');
  console.log('✓ Network errors trigger retry attempts');
  console.log('✓ Circuit breaker status monitoring');
  console.log('✓ Concurrent request handling');
  console.log('✓ Error response formatting');

  if (testResults.passed === testResults.total) {
    console.log('\n🎉 All tests passed! Retry mechanism working correctly.');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some tests failed. Please check the retry mechanism.');
    process.exit(1);
  }
}

// Error handling
process.on('unhandledRejection', (reason) => {
  console.error('\nUnhandled rejection:', reason);
  process.exit(1);
});

// Run tests
runTests().catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});
