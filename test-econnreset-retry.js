#!/usr/bin/env node

/**
 * ECONNRESET重试机制测试
 * 验证修复后的重试逻辑能够正确处理ECONNRESET错误
 */

import { callElevenLabsAPI } from './lib/utils.js';

console.log('🧪 ECONNRESET重试机制测试');
console.log('=' .repeat(50));

// 模拟ECONNRESET错误的测试
async function testECONNRESETRetry() {
  console.log('\n📋 测试场景: ECONNRESET错误重试');
  console.log('预期行为: 应该触发重试机制');
  
  const startTime = Date.now();
  
  try {
    // 使用无效的voice ID来触发网络错误
    const result = await callElevenLabsAPI(
      'invalid_voice_that_causes_network_error',
      JSON.stringify({ text: 'Test ECONNRESET retry' }),
      30000 // 30秒超时
    );
    
    console.log('❌ 意外成功 - 应该失败并重试');
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    console.log(`\n📊 测试结果:`);
    console.log(`   总耗时: ${duration}ms`);
    console.log(`   错误类型: ${error.name}`);
    console.log(`   错误代码: ${error.code || 'N/A'}`);
    console.log(`   错误消息: ${error.message.substring(0, 200)}...`);
    
    // 分析是否有重试迹象
    const hasRetryDelay = duration > 2000; // 超过2秒表示可能有重试
    
    if (hasRetryDelay) {
      console.log(`   ✅ 检测到重试延迟 (${duration}ms > 2000ms)`);
      console.log(`   ✅ 重试机制可能已生效`);
    } else {
      console.log(`   ❌ 未检测到重试延迟 (${duration}ms <= 2000ms)`);
      console.log(`   ❌ 重试机制可能未生效`);
    }
    
    // 检查错误类型
    if (error.code === 'ECONNRESET' || error.message.includes('ECONNRESET')) {
      console.log(`   ✅ 确认为ECONNRESET类型错误`);
    } else if (error.message.includes('socket disconnected') || error.message.includes('TLS connection')) {
      console.log(`   ✅ 确认为网络连接错误`);
    } else {
      console.log(`   ℹ️  其他类型错误: ${error.code || error.name}`);
    }
  }
}

// 运行测试
async function runTests() {
  try {
    await testECONNRESETRetry();
    
    console.log('\n🎯 测试说明:');
    console.log('1. 如果看到"检测到重试延迟"，说明重试机制已生效');
    console.log('2. 查看日志中的"[REGION] 🔍 Analyzing retry eligibility"部分');
    console.log('3. 确认"Is retryable error: true"表示错误被正确识别为可重试');
    console.log('4. 如果仍显示"Non-retryable error"，请检查错误代码匹配逻辑');
    
  } catch (testError) {
    console.error('❌ 测试执行失败:', testError.message);
  }
}

// 启动测试
runTests().catch(console.error);
