// test-error-scenarios.js - 专门测试各种错误场景的独立测试文件
// 重点测试401、403、quota_exceeded等不应重试的场景

import fetch from 'node-fetch';

// 测试配置
const CONFIG = {
  BASE_URL: process.env.TEST_BASE_URL || 'http://localhost:3007',
  PROXY_SECRET: process.env.PROXY_SECRET || 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9',
  TEST_VOICE_ID: 'test_voice_id',
  TIMEOUT: 10000
};

console.log('🧪 错误场景专项测试');
console.log(`📍 测试目标: ${CONFIG.BASE_URL}`);
console.log(`🔑 认证密钥: ${CONFIG.PROXY_SECRET.substring(0, 4)}...`);

// 测试结果收集器
class TestCollector {
  constructor() {
    this.results = [];
    this.summary = { total: 0, passed: 0, failed: 0 };
  }

  record(testName, passed, details = '', duration = 0) {
    this.summary.total++;
    if (passed) {
      this.summary.passed++;
    } else {
      this.summary.failed++;
    }

    this.results.push({
      name: testName,
      passed,
      details,
      duration,
      timestamp: new Date().toISOString()
    });

    const status = passed ? '✅' : '❌';
    console.log(`   ${status} ${testName} (${duration}ms)`);
    if (details && !passed) {
      console.log(`      ${details}`);
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 错误场景测试结果');
    console.log('='.repeat(60));
    
    console.log(`\n📈 统计信息:`);
    console.log(`   总测试: ${this.summary.total}`);
    console.log(`   通过: ${this.summary.passed} ✅`);
    console.log(`   失败: ${this.summary.failed} ❌`);
    console.log(`   成功率: ${Math.round((this.summary.passed / this.summary.total) * 100)}%`);

    if (this.summary.failed > 0) {
      console.log(`\n❌ 失败详情:`);
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`   • ${r.name}: ${r.details}`);
        });
    }

    console.log(`\n🎯 测试重点验证:`);
    console.log(`   ✓ 401 Unauthorized - 认证失败不重试`);
    console.log(`   ✓ 403 Forbidden - 权限不足不重试`);
    console.log(`   ✓ 429 Quota Exceeded - 配额超限不重试`);
    console.log(`   ✓ 网络错误 - 应该触发重试机制`);
    console.log(`   ✓ 响应时间 - 验证重试延迟策略`);

    return this.summary.passed === this.summary.total;
  }
}

const collector = new TestCollector();

// 工具函数：发送测试请求
async function sendRequest(options = {}) {
  const {
    voiceId = CONFIG.TEST_VOICE_ID,
    body = { text: 'Test message' },
    headers = {},
    expectError = false
  } = options;

  const url = `${CONFIG.BASE_URL}/api/v1/text-to-speech/${voiceId}`;
  const startTime = Date.now();

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': CONFIG.PROXY_SECRET,
        ...headers
      },
      body: JSON.stringify(body),
      timeout: CONFIG.TIMEOUT
    });

    const duration = Date.now() - startTime;
    let responseBody;

    try {
      responseBody = await response.json();
    } catch {
      responseBody = await response.text();
    }

    return {
      status: response.status,
      statusText: response.statusText,
      body: responseBody,
      headers: Object.fromEntries(response.headers.entries()),
      duration
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      error: error.message,
      name: error.name,
      duration,
      isNetworkError: error.code === 'ECONNREFUSED' || 
                     error.code === 'ENOTFOUND' || 
                     error.code === 'ETIMEDOUT' ||
                     error.message.includes('timeout')
    };
  }
}

// 测试1: 401 Unauthorized - 无效密钥
async function test401Unauthorized() {
  console.log('\n🔍 测试 401 Unauthorized 场景');
  
  const response = await sendRequest({
    headers: { 'x-proxy-secret': 'invalid_secret_12345' }
  });

  // 验证状态码
  collector.record(
    '401错误状态码',
    response.status === 401,
    `期望401，实际${response.status}`,
    response.duration
  );

  // 验证响应时间（不应重试，应该很快返回）
  collector.record(
    '401错误响应时间',
    response.duration < 2000,
    `响应时间${response.duration}ms，不应重试`,
    response.duration
  );

  // 验证错误信息
  const hasErrorInfo = response.body && 
                      (response.body.error || response.body.message);
  collector.record(
    '401错误信息',
    hasErrorInfo,
    `包含错误描述: ${!!hasErrorInfo}`,
    response.duration
  );
}

// 测试2: 403 Forbidden - 权限不足
async function test403Forbidden() {
  console.log('\n🔍 测试 403 Forbidden 场景');
  
  // 模拟权限不足的请求（使用特殊的语音ID）
  const response = await sendRequest({
    voiceId: 'forbidden_voice_id',
    body: { text: 'This should be forbidden' }
  });

  // 注意：实际的403可能需要特定的服务器配置
  // 这里我们主要测试客户端处理逻辑
  const isForbiddenOrOtherError = response.status === 403 || 
                                 response.status >= 400;
  
  collector.record(
    '403或其他客户端错误',
    isForbiddenOrOtherError,
    `状态码: ${response.status}`,
    response.duration
  );

  // 验证不重试（快速响应）
  collector.record(
    '403错误快速响应',
    response.duration < 3000,
    `响应时间${response.duration}ms`,
    response.duration
  );
}

// 测试3: 429 Quota Exceeded - 配额超限
async function test429QuotaExceeded() {
  console.log('\n🔍 测试 429 Quota Exceeded 场景');
  
  // 发送大量请求尝试触发限流
  const requests = [];
  for (let i = 0; i < 5; i++) {
    requests.push(sendRequest({
      body: { text: `Quota test message ${i + 1}` }
    }));
  }

  const responses = await Promise.all(requests);
  
  // 检查是否有429响应
  const hasRateLimit = responses.some(r => r.status === 429);
  const has503CircuitBreaker = responses.some(r => r.status === 503);
  
  collector.record(
    '限流或熔断保护',
    hasRateLimit || has503CircuitBreaker,
    `检测到限流(429)或熔断(503)保护`,
    Math.max(...responses.map(r => r.duration))
  );

  // 验证限流响应包含重试信息
  const rateLimitResponse = responses.find(r => r.status === 429);
  if (rateLimitResponse) {
    const hasRetryAfter = rateLimitResponse.body && 
                         rateLimitResponse.body.retryAfter;
    collector.record(
      '429响应包含重试信息',
      hasRetryAfter,
      `retryAfter: ${rateLimitResponse.body?.retryAfter}`,
      rateLimitResponse.duration
    );
  }
}

// 测试4: 网络错误重试机制
async function testNetworkErrorRetry() {
  console.log('\n🔍 测试网络错误重试机制');
  
  // 尝试连接到不存在的端口来模拟网络错误
  const invalidUrl = 'http://localhost:99999/api/v1/text-to-speech/test';
  const startTime = Date.now();
  
  try {
    await fetch(invalidUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'Network error test' }),
      timeout: 3000
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // 验证是网络错误
    const isNetworkError = error.code === 'ECONNREFUSED' || 
                          error.message.includes('ECONNREFUSED');
    
    collector.record(
      '网络错误检测',
      isNetworkError,
      `错误类型: ${error.code || error.name}`,
      duration
    );

    // 验证超时时间合理（应该尝试连接）
    collector.record(
      '网络错误超时',
      duration >= 2000 && duration <= 5000,
      `超时时间: ${duration}ms`,
      duration
    );
  }
}

// 测试5: 电路熔断器集成测试
async function testCircuitBreakerIntegration() {
  console.log('\n🔍 测试电路熔断器集成');
  
  // 检查健康检查端点的电路熔断器信息
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/api/health`, {
      headers: { 'x-proxy-secret': CONFIG.PROXY_SECRET }
    });
    
    const healthData = await response.json();
    
    // 验证电路熔断器状态信息
    const hasCircuitBreakerInfo = healthData.circuitBreaker && 
                                 healthData.circuitBreaker.status;
    
    collector.record(
      '电路熔断器状态可见',
      hasCircuitBreakerInfo,
      `状态: ${healthData.circuitBreaker?.status}`,
      0
    );

    // 验证统计信息
    const hasStats = healthData.circuitBreaker?.statistics &&
                    typeof healthData.circuitBreaker.statistics.totalRequests === 'number';
    
    collector.record(
      '电路熔断器统计信息',
      hasStats,
      `总请求: ${healthData.circuitBreaker?.statistics?.totalRequests}`,
      0
    );

  } catch (error) {
    collector.record(
      '健康检查可访问性',
      false,
      `错误: ${error.message}`,
      0
    );
  }
}

// 测试6: 重试延迟验证
async function testRetryDelayValidation() {
  console.log('\n🔍 测试重试延迟验证');
  
  // 发送一个可能触发重试的请求
  const startTime = Date.now();
  const response = await sendRequest({
    body: { text: 'Retry delay test message' }
  });
  const totalDuration = Date.now() - startTime;

  // 如果响应时间较长，可能包含了重试延迟
  const possibleRetryDelay = totalDuration > 2000;
  
  collector.record(
    '重试延迟检测',
    true, // 总是通过，因为这更多是观察性测试
    `总耗时: ${totalDuration}ms, 可能包含重试: ${possibleRetryDelay}`,
    totalDuration
  );

  // 验证最终响应
  const hasValidResponse = response.status !== undefined || response.error !== undefined;
  collector.record(
    '重试后响应有效性',
    hasValidResponse,
    `状态: ${response.status || response.error}`,
    totalDuration
  );
}

// 主测试执行函数
async function runErrorScenarioTests() {
  console.log('🚀 开始执行错误场景专项测试...\n');

  // 检查服务器状态
  console.log('🔍 检查服务器连接...');
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/`, {
      timeout: 5000,
      headers: { 'x-proxy-secret': CONFIG.PROXY_SECRET }
    });

    if (!response.ok && response.status !== 401) {
      throw new Error(`服务器响应异常: ${response.status}`);
    }
    console.log('✅ 服务器连接正常\n');
  } catch (error) {
    console.error('❌ 无法连接到服务器:', error.message);
    console.log('💡 请确保服务器正在运行:');
    console.log('   npm start');
    console.log('   或 pm2 start ecosystem.config.cjs');
    process.exit(1);
  }

  // 执行所有测试
  try {
    await test401Unauthorized();
    await test403Forbidden();
    await test429QuotaExceeded();
    await testNetworkErrorRetry();
    await testCircuitBreakerIntegration();
    await testRetryDelayValidation();

    // 额外的边界测试
    await testEdgeCases();

  } catch (error) {
    console.error('\n❌ 测试执行过程中发生错误:', error);
    collector.record('测试执行', false, error.message, 0);
  }

  // 输出结果
  const allPassed = collector.printSummary();

  if (allPassed) {
    console.log('\n🎉 所有错误场景测试通过！');
    process.exit(0);
  } else {
    console.log('\n⚠️ 部分测试失败，请检查错误处理逻辑。');
    process.exit(1);
  }
}

// 边界情况测试
async function testEdgeCases() {
  console.log('\n🔍 测试边界情况');

  // 测试空请求体
  const emptyBodyResponse = await sendRequest({
    body: {}
  });

  collector.record(
    '空请求体处理',
    emptyBodyResponse.status >= 400,
    `状态: ${emptyBodyResponse.status}`,
    emptyBodyResponse.duration
  );

  // 测试超大请求体
  const largeBodyResponse = await sendRequest({
    body: { text: 'x'.repeat(50000) }
  });

  collector.record(
    '超大请求体处理',
    largeBodyResponse.status === 413 || largeBodyResponse.status >= 400,
    `状态: ${largeBodyResponse.status}`,
    largeBodyResponse.duration
  );

  // 测试无效JSON
  try {
    const invalidJsonResponse = await fetch(`${CONFIG.BASE_URL}/api/v1/text-to-speech/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': CONFIG.PROXY_SECRET
      },
      body: 'invalid json content',
      timeout: CONFIG.TIMEOUT
    });

    collector.record(
      '无效JSON处理',
      invalidJsonResponse.status >= 400,
      `状态: ${invalidJsonResponse.status}`,
      0
    );
  } catch (error) {
    collector.record(
      '无效JSON处理',
      true,
      `正确拒绝: ${error.message}`,
      0
    );
  }

  // 测试缺失Content-Type
  const noContentTypeResponse = await fetch(`${CONFIG.BASE_URL}/api/v1/text-to-speech/test`, {
    method: 'POST',
    headers: {
      'x-proxy-secret': CONFIG.PROXY_SECRET
    },
    body: JSON.stringify({ text: 'Test without content-type' }),
    timeout: CONFIG.TIMEOUT
  });

  collector.record(
    '缺失Content-Type处理',
    noContentTypeResponse.status >= 400 || noContentTypeResponse.ok,
    `状态: ${noContentTypeResponse.status}`,
    0
  );
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('\n❌ 未处理的Promise拒绝:', reason);
  collector.record('未处理错误', false, reason.toString(), 0);
  collector.printSummary();
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('\n❌ 未捕获的异常:', error);
  collector.record('未捕获异常', false, error.message, 0);
  collector.printSummary();
  process.exit(1);
});

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runErrorScenarioTests().catch(error => {
    console.error('❌ 测试启动失败:', error);
    process.exit(1);
  });
}

// 导出测试函数供其他模块使用
export {
  runErrorScenarioTests,
  test401Unauthorized,
  test403Forbidden,
  test429QuotaExceeded,
  testNetworkErrorRetry,
  testCircuitBreakerIntegration,
  CONFIG
};
