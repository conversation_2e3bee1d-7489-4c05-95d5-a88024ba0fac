// test-global-suffix.js - 测试静态地区的-global后缀功能

import dotenv from 'dotenv';
import { getDynamicProxyConfig } from './lib/utils.js';

// 加载环境变量
dotenv.config();

console.log('🧪 静态地区-global后缀功能测试开始...\n');

// 测试静态模式
async function testStaticMode() {
  console.log('📋 测试1: 静态模式（ENABLE_DYNAMIC_REGIONS=false）');
  
  // 设置为静态模式
  const originalValue = process.env.ENABLE_DYNAMIC_REGIONS;
  process.env.ENABLE_DYNAMIC_REGIONS = 'false';
  
  try {
    const config = getDynamicProxyConfig();
    
    console.log('静态模式配置结果:');
    console.log(`  PROXY_IP: ${config.PROXY_IP}`);
    console.log(`  PROXY_PORT: ${config.PROXY_PORT}`);
    console.log(`  PROXY_USERNAME: ${config.PROXY_USERNAME}`);
    console.log(`  PROXY_PASSWORD: ${config.PROXY_PASSWORD}`);
    console.log(`  SELECTED_REGION: ${config.SELECTED_REGION}`);
    
    // 验证密码是否正确添加了-global后缀
    const basePassword = process.env.PROXY_PASSWORD;
    const expectedPassword = basePassword + '-global';
    
    if (config.PROXY_PASSWORD === expectedPassword) {
      console.log('✅ 密码后缀验证成功');
      console.log(`   基础密码: ${basePassword}`);
      console.log(`   期望密码: ${expectedPassword}`);
      console.log(`   实际密码: ${config.PROXY_PASSWORD}`);
    } else {
      console.log('❌ 密码后缀验证失败');
      console.log(`   基础密码: ${basePassword}`);
      console.log(`   期望密码: ${expectedPassword}`);
      console.log(`   实际密码: ${config.PROXY_PASSWORD}`);
    }
    
  } catch (error) {
    console.log('❌ 静态模式测试失败:', error.message);
  } finally {
    // 恢复原始环境变量
    if (originalValue !== undefined) {
      process.env.ENABLE_DYNAMIC_REGIONS = originalValue;
    } else {
      delete process.env.ENABLE_DYNAMIC_REGIONS;
    }
  }
  
  console.log('');
}

// 测试动态模式
async function testDynamicMode() {
  console.log('📋 测试2: 动态模式（ENABLE_DYNAMIC_REGIONS=true）');
  
  // 设置为动态模式
  const originalValue = process.env.ENABLE_DYNAMIC_REGIONS;
  process.env.ENABLE_DYNAMIC_REGIONS = 'true';
  
  try {
    const config = getDynamicProxyConfig();
    
    console.log('动态模式配置结果:');
    console.log(`  PROXY_IP: ${config.PROXY_IP}`);
    console.log(`  PROXY_PORT: ${config.PROXY_PORT}`);
    console.log(`  PROXY_USERNAME: ${config.PROXY_USERNAME}`);
    console.log(`  PROXY_PASSWORD: ${config.PROXY_PASSWORD}`);
    console.log(`  SELECTED_REGION: ${config.SELECTED_REGION ? `${config.SELECTED_REGION.countryCode}_${config.SELECTED_REGION.regionCode}_city_${config.SELECTED_REGION.cityCode}` : 'null'}`);
    
    // 验证动态模式的密码格式
    const basePassword = process.env.PROXY_PASSWORD;
    
    if (config.SELECTED_REGION) {
      const expectedSuffix = `-${config.SELECTED_REGION.countryCode}_${config.SELECTED_REGION.regionCode}_city_${config.SELECTED_REGION.cityCode}`;
      const expectedPassword = basePassword + expectedSuffix;
      
      if (config.PROXY_PASSWORD === expectedPassword) {
        console.log('✅ 动态密码格式验证成功');
        console.log(`   基础密码: ${basePassword}`);
        console.log(`   地区后缀: ${expectedSuffix}`);
        console.log(`   完整密码: ${config.PROXY_PASSWORD}`);
      } else {
        console.log('❌ 动态密码格式验证失败');
        console.log(`   基础密码: ${basePassword}`);
        console.log(`   期望密码: ${expectedPassword}`);
        console.log(`   实际密码: ${config.PROXY_PASSWORD}`);
      }
    } else {
      console.log('⚠️ 动态模式但未选择地区（可能是地区数据问题）');
    }
    
  } catch (error) {
    console.log('❌ 动态模式测试失败:', error.message);
  } finally {
    // 恢复原始环境变量
    if (originalValue !== undefined) {
      process.env.ENABLE_DYNAMIC_REGIONS = originalValue;
    } else {
      delete process.env.ENABLE_DYNAMIC_REGIONS;
    }
  }
  
  console.log('');
}

// 测试未设置环境变量的情况
async function testUndefinedMode() {
  console.log('📋 测试3: 未设置环境变量（默认行为）');
  
  // 删除环境变量
  const originalValue = process.env.ENABLE_DYNAMIC_REGIONS;
  delete process.env.ENABLE_DYNAMIC_REGIONS;
  
  try {
    const config = getDynamicProxyConfig();
    
    console.log('未设置环境变量的配置结果:');
    console.log(`  PROXY_PASSWORD: ${config.PROXY_PASSWORD}`);
    
    // 验证默认行为（应该是静态模式）
    const basePassword = process.env.PROXY_PASSWORD;
    const expectedPassword = basePassword + '-global';
    
    if (config.PROXY_PASSWORD === expectedPassword) {
      console.log('✅ 默认行为验证成功（静态模式 + global后缀）');
    } else {
      console.log('❌ 默认行为验证失败');
    }
    
  } catch (error) {
    console.log('❌ 未设置环境变量测试失败:', error.message);
  } finally {
    // 恢复原始环境变量
    if (originalValue !== undefined) {
      process.env.ENABLE_DYNAMIC_REGIONS = originalValue;
    }
  }
  
  console.log('');
}

// 执行所有测试
async function runAllTests() {
  console.log(`当前环境变量 ENABLE_DYNAMIC_REGIONS: ${process.env.ENABLE_DYNAMIC_REGIONS || '未设置'}`);
  console.log(`当前基础密码 PROXY_PASSWORD: ${process.env.PROXY_PASSWORD}\n`);
  
  await testStaticMode();
  await testDynamicMode();
  await testUndefinedMode();
  
  console.log('🎯 测试总结:');
  console.log('1. 静态模式：密码应该是 "基础密码-global"');
  console.log('2. 动态模式：密码应该是 "基础密码-地区信息"');
  console.log('3. 未设置环境变量：默认使用静态模式');
  console.log('4. 所有模式都应该正确构建认证信息');
}

// 启动测试
runAllTests().catch(error => {
  console.error('测试执行失败:', error);
});
