#!/usr/bin/env node
// run-retry-tests.js - 重试机制测试运行器
// 统一执行所有重试相关的测试

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 测试配置
const TEST_CONFIG = {
  BASE_URL: process.env.TEST_BASE_URL || 'http://localhost:3007',
  PROXY_SECRET: process.env.PROXY_SECRET || 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9',
  TIMEOUT: 30000 // 30秒超时
};

console.log('🧪 重试机制测试套件');
console.log('='.repeat(50));
console.log(`📍 测试目标: ${TEST_CONFIG.BASE_URL}`);
console.log(`🔑 认证密钥: ${TEST_CONFIG.PROXY_SECRET.substring(0, 4)}...`);
console.log(`⏱️  超时设置: ${TEST_CONFIG.TIMEOUT}ms`);
console.log('='.repeat(50));

// 测试文件列表
const TEST_FILES = [
  {
    name: '错误场景专项测试',
    file: 'test-error-scenarios.js',
    description: '测试401、403、quota_exceeded等错误场景'
  },
  {
    name: '重试机制综合测试',
    file: 'test-retry-mechanisms.js',
    description: '测试电路熔断器、并发控制、重试逻辑'
  }
];

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

// 运行单个测试文件
function runTestFile(testFile) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 运行: ${testFile.name}`);
    console.log(`📄 文件: ${testFile.file}`);
    console.log(`📝 描述: ${testFile.description}`);
    console.log('-'.repeat(40));

    const testPath = join(__dirname, testFile.file);
    const child = spawn('node', [testPath], {
      stdio: 'inherit',
      env: {
        ...process.env,
        TEST_BASE_URL: TEST_CONFIG.BASE_URL,
        PROXY_SECRET: TEST_CONFIG.PROXY_SECRET
      }
    });

    const timeout = setTimeout(() => {
      child.kill('SIGTERM');
      reject(new Error(`测试超时: ${testFile.name}`));
    }, TEST_CONFIG.TIMEOUT);

    child.on('close', (code) => {
      clearTimeout(timeout);
      
      if (code === 0) {
        console.log(`✅ ${testFile.name} - 通过`);
        testResults.passed++;
        resolve({ name: testFile.name, passed: true, code });
      } else {
        console.log(`❌ ${testFile.name} - 失败 (退出码: ${code})`);
        testResults.failed++;
        resolve({ name: testFile.name, passed: false, code });
      }
    });

    child.on('error', (error) => {
      clearTimeout(timeout);
      console.error(`❌ ${testFile.name} - 执行错误:`, error.message);
      testResults.failed++;
      reject(error);
    });
  });
}

// 检查服务器状态
async function checkServerStatus() {
  console.log('\n🔍 检查服务器状态...');
  
  try {
    const fetch = (await import('node-fetch')).default;
    const response = await fetch(`${TEST_CONFIG.BASE_URL}/`, { 
      timeout: 5000 
    });
    
    if (response.ok || response.status === 401) {
      console.log('✅ 服务器运行正常');
      return true;
    } else {
      console.log(`⚠️ 服务器响应异常: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error('❌ 无法连接到服务器:', error.message);
    console.log('\n💡 请先启动服务器:');
    console.log('   npm start');
    console.log('   或 pm2 start ecosystem.config.cjs');
    return false;
  }
}

// 主测试执行函数
async function runAllTests() {
  console.log('\n🚀 开始执行重试机制测试套件...');
  
  // 检查服务器状态
  const serverOk = await checkServerStatus();
  if (!serverOk) {
    console.log('\n❌ 服务器不可用，跳过测试');
    process.exit(1);
  }

  // 执行所有测试
  testResults.total = TEST_FILES.length;
  
  for (const testFile of TEST_FILES) {
    try {
      const result = await runTestFile(testFile);
      testResults.details.push(result);
    } catch (error) {
      console.error(`❌ 测试执行失败: ${testFile.name}`, error.message);
      testResults.details.push({
        name: testFile.name,
        passed: false,
        error: error.message
      });
    }
  }

  // 输出最终结果
  printFinalResults();
}

// 打印最终测试结果
function printFinalResults() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 重试机制测试套件 - 最终结果');
  console.log('='.repeat(60));

  console.log(`\n📈 总体统计:`);
  console.log(`   测试文件总数: ${testResults.total}`);
  console.log(`   通过: ${testResults.passed} ✅`);
  console.log(`   失败: ${testResults.failed} ❌`);
  console.log(`   成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);

  console.log(`\n📋 详细结果:`);
  testResults.details.forEach((result, index) => {
    const status = result.passed ? '✅' : '❌';
    const code = result.code !== undefined ? ` (退出码: ${result.code})` : '';
    const error = result.error ? ` - ${result.error}` : '';
    console.log(`   ${index + 1}. ${status} ${result.name}${code}${error}`);
  });

  if (testResults.failed > 0) {
    console.log(`\n❌ 失败的测试:`);
    testResults.details
      .filter(r => !r.passed)
      .forEach(r => {
        console.log(`   • ${r.name}`);
        if (r.error) {
          console.log(`     错误: ${r.error}`);
        }
      });
  }

  console.log(`\n🎯 重试机制验证要点:`);
  console.log(`   ✓ 401/403错误不重试 - 避免无效认证尝试`);
  console.log(`   ✓ quota_exceeded不重试 - 保护配额不被浪费`);
  console.log(`   ✓ 网络错误重试 - 提高系统可用性`);
  console.log(`   ✓ 电路熔断器保护 - 防止级联故障`);
  console.log(`   ✓ 并发控制 - 避免系统过载`);
  console.log(`   ✓ 重试延迟策略 - 指数退避算法`);

  console.log(`\n📚 测试覆盖范围:`);
  console.log(`   • HTTP状态码处理 (401, 403, 429, 500, 502, 503)`);
  console.log(`   • 网络错误处理 (ECONNREFUSED, ETIMEDOUT, ECONNRESET)`);
  console.log(`   • 电路熔断器状态转换 (CLOSED → OPEN → HALF_OPEN)`);
  console.log(`   • 重试延迟验证 (1s → 1.5s → 2s)`);
  console.log(`   • 并发请求控制`);
  console.log(`   • 健康检查集成`);

  if (testResults.passed === testResults.total) {
    console.log(`\n🎉 所有测试通过！重试机制工作正常。`);
    console.log(`\n✨ 系统具备以下重试能力:`);
    console.log(`   • 智能错误分类 - 区分可重试和不可重试错误`);
    console.log(`   • 自适应保护 - 电路熔断器防止雪崩`);
    console.log(`   • 优雅降级 - 并发控制和限流保护`);
    console.log(`   • 可观测性 - 完整的监控和统计信息`);
    process.exit(0);
  } else {
    console.log(`\n⚠️ 部分测试失败，请检查重试机制配置。`);
    console.log(`\n🔧 故障排除建议:`);
    console.log(`   1. 检查服务器是否正常运行`);
    console.log(`   2. 验证环境变量配置 (PROXY_SECRET等)`);
    console.log(`   3. 确认代理服务器连接正常`);
    console.log(`   4. 查看服务器日志获取详细错误信息`);
    process.exit(1);
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('\n❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('\n❌ 未捕获的异常:', error);
  process.exit(1);
});

// 信号处理
process.on('SIGINT', () => {
  console.log('\n\n⏹️ 测试被用户中断');
  process.exit(130);
});

process.on('SIGTERM', () => {
  console.log('\n\n⏹️ 测试被终止');
  process.exit(143);
});

// 执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('❌ 测试套件启动失败:', error);
    process.exit(1);
  });
}

export { runAllTests, TEST_CONFIG };
