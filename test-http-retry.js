// test-http-retry.js - 测试新的HTTP错误重试机制

import fetch from 'node-fetch';

const CONFIG = {
  BASE_URL: 'http://localhost:3007',
  PROXY_SECRET: 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9',
  TEST_VOICE_ID: 'test_voice_id'
};

console.log('🧪 HTTP错误重试机制测试');
console.log(`📍 测试目标: ${CONFIG.BASE_URL}`);
console.log(`🔑 认证密钥: ${CONFIG.PROXY_SECRET.substring(0, 4)}...`);
console.log('');

// 测试结果收集
let testResults = { total: 0, passed: 0, failed: 0 };

function recordResult(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}`);
    if (details) console.log(`   ${details}`);
  }
}

async function sendRequest(options = {}) {
  const {
    voiceId = CONFIG.TEST_VOICE_ID,
    body = { text: 'Test HTTP retry mechanism' },
    headers = {}
  } = options;

  const url = `${CONFIG.BASE_URL}/api/v1/text-to-speech/${voiceId}`;
  const startTime = Date.now();

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': CONFIG.PROXY_SECRET,
        ...headers
      },
      body: JSON.stringify(body),
      timeout: 30000
    });

    const duration = Date.now() - startTime;
    let responseBody;

    try {
      responseBody = await response.json();
    } catch {
      responseBody = await response.text();
    }

    return {
      status: response.status,
      body: responseBody,
      duration
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      error: error.message,
      duration
    };
  }
}

// 测试1: 验证401错误重试机制
async function test401Retry() {
  console.log('\n--- 测试401错误重试机制 ---');
  
  const response = await sendRequest({
    headers: { 'x-proxy-secret': 'invalid_secret_for_retry_test' }
  });

  // 验证响应时间（应该包含重试延迟）
  const hasRetryDelay = response.duration > 3000; // 至少应该有2次重试的延迟
  recordResult(
    '401错误重试延迟检测',
    hasRetryDelay,
    `响应时间: ${response.duration}ms (应该 > 3000ms 表示有重试)`
  );

  // 验证最终状态码
  recordResult(
    '401错误最终状态码',
    response.status === 401,
    `最终状态: ${response.status}`
  );
}

// 测试2: 验证正常请求不重试
async function testSuccessNoRetry() {
  console.log('\n--- 测试正常请求不重试 ---');
  
  // 发送一个会导致ElevenLabs API错误但不是401的请求
  const response = await sendRequest({
    body: { text: 'Normal request test' }
  });

  // 检查响应（可能是502、503等，但应该有重试机制）
  console.log(`响应状态: ${response.status || 'Error'}`);
  console.log(`响应时间: ${response.duration}ms`);
  
  recordResult(
    '请求完成',
    response.status !== undefined || response.error !== undefined,
    `状态: ${response.status || response.error}`
  );
}

// 测试3: 验证空请求体重试机制
async function testEmptyBodyRetry() {
  console.log('\n--- 测试空请求体重试机制 ---');
  
  const response = await sendRequest({
    body: {}
  });

  // 验证响应时间（400错误应该也会重试）
  const hasRetryDelay = response.duration > 2000;
  recordResult(
    '空请求体重试延迟检测',
    hasRetryDelay,
    `响应时间: ${response.duration}ms (应该有重试延迟)`
  );

  // 验证最终状态码
  recordResult(
    '空请求体最终状态码',
    response.status >= 400,
    `最终状态: ${response.status}`
  );
}

// 测试4: 验证重试机制配置
async function testRetryConfiguration() {
  console.log('\n--- 测试重试机制配置 ---');
  
  // 检查健康状态
  try {
    const healthResponse = await fetch(`${CONFIG.BASE_URL}/api/health`, {
      headers: { 'x-proxy-secret': CONFIG.PROXY_SECRET }
    });
    
    const healthData = await healthResponse.json();
    
    recordResult(
      '健康检查可访问',
      healthResponse.status === 200,
      `状态: ${healthResponse.status}`
    );

    recordResult(
      '电路熔断器信息',
      healthData.circuitBreaker && healthData.circuitBreaker.status,
      `电路熔断器状态: ${healthData.circuitBreaker?.status}`
    );

  } catch (error) {
    recordResult(
      '健康检查错误',
      false,
      `错误: ${error.message}`
    );
  }
}

// 主测试函数
async function runHttpRetryTests() {
  console.log('🚀 开始HTTP错误重试机制测试...\n');
  
  // 检查服务器状态
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/`, { timeout: 5000 });
    if (!response.ok && response.status !== 401) {
      throw new Error(`服务器响应异常: ${response.status}`);
    }
    console.log('✅ 服务器连接正常\n');
  } catch (error) {
    console.error('❌ 无法连接到服务器:', error.message);
    console.log('💡 请确保服务器正在运行: npm start');
    process.exit(1);
  }

  // 执行测试
  try {
    await test401Retry();
    await testSuccessNoRetry();
    await testEmptyBodyRetry();
    await testRetryConfiguration();
  } catch (error) {
    console.error('\n❌ 测试执行错误:', error);
    recordResult('测试执行', false, error.message);
  }

  // 输出结果
  console.log('\n' + '='.repeat(50));
  console.log('📊 HTTP错误重试机制测试结果');
  console.log('='.repeat(50));
  console.log(`总测试: ${testResults.total}`);
  console.log(`通过: ${testResults.passed} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  console.log(`成功率: ${Math.round((testResults.passed / testResults.total) * 100)}%`);

  console.log('\n🎯 新重试机制验证要点:');
  console.log('✓ 401/403错误重试 - 最大2次重试');
  console.log('✓ 429错误重试 - 最大2次重试（包括quota_exceeded）');
  console.log('✓ 其他HTTP错误重试 - 最大1次重试');
  console.log('✓ 短延迟策略 - 1-3秒随机延迟');
  console.log('✓ 网络错误保持原有重试机制');

  if (testResults.passed === testResults.total) {
    console.log('\n🎉 HTTP错误重试机制测试通过！');
    process.exit(0);
  } else {
    console.log('\n⚠️ 部分测试失败，请检查重试机制实现。');
    process.exit(1);
  }
}

// 错误处理
process.on('unhandledRejection', (reason) => {
  console.error('\n❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行测试
runHttpRetryTests().catch(error => {
  console.error('❌ 测试启动失败:', error);
  process.exit(1);
});
