// test-dynamic-regions.js - 动态地区功能测试脚本

import dotenv from 'dotenv';
import { 
  loadRegionsData, 
  selectRandomRegion, 
  buildDynamicProxyAuth, 
  getRegionsStats,
  validateRegionsData 
} from './lib/region-manager.js';

// 加载环境变量
dotenv.config();

console.log('🧪 动态地区功能测试开始...\n');

// 测试1: 地区数据验证
console.log('📋 测试1: 地区数据验证');
const validation = validateRegionsData();
console.log('验证结果:', validation);
console.log('');

// 测试2: 地区数据加载
console.log('📋 测试2: 地区数据加载');
const regions = loadRegionsData();
console.log(`加载的地区数量: ${regions.length}`);
if (regions.length > 0) {
  console.log('前3个地区示例:', regions.slice(0, 3));
}
console.log('');

// 测试3: 地区统计信息
console.log('📋 测试3: 地区统计信息');
const stats = getRegionsStats();
console.log('统计信息:', stats);
console.log('');

// 测试4: 随机地区选择
console.log('📋 测试4: 随机地区选择');
for (let i = 0; i < 5; i++) {
  const selectedRegion = selectRandomRegion();
  if (selectedRegion) {
    console.log(`选择 ${i + 1}: ${selectedRegion.countryCode}_${selectedRegion.regionCode}_city_${selectedRegion.cityCode}`);
  } else {
    console.log(`选择 ${i + 1}: 无可用地区`);
  }
}
console.log('');

// 测试5: 动态代理认证构建
console.log('📋 测试5: 动态代理认证构建');
const baseUsername = '4627768-8c4b0cb7';
const basePassword = '14ac4e67';

// 测试固定认证
const staticAuth = buildDynamicProxyAuth(baseUsername, basePassword, null);
console.log('固定认证:', staticAuth);

// 测试动态认证
const testRegion = selectRandomRegion();
if (testRegion) {
  const dynamicAuth = buildDynamicProxyAuth(baseUsername, basePassword, testRegion);
  console.log('动态认证:', dynamicAuth);
  console.log(`完整格式: ${dynamicAuth.username}:${dynamicAuth.password}`);
}
console.log('');

// 测试6: 环境变量检查
console.log('📋 测试6: 环境变量检查');
console.log(`ENABLE_DYNAMIC_REGIONS: ${process.env.ENABLE_DYNAMIC_REGIONS}`);
console.log(`PROXY_IP: ${process.env.PROXY_IP || '未设置'}`);
console.log(`PROXY_PORT: ${process.env.PROXY_PORT || '未设置'}`);
console.log(`PROXY_USERNAME: ${process.env.PROXY_USERNAME || '未设置'}`);
console.log(`PROXY_PASSWORD: ${process.env.PROXY_PASSWORD ? '已设置' : '未设置'}`);
console.log('');

console.log('✅ 动态地区功能测试完成！');

// 测试7: 模拟API调用配置
console.log('📋 测试7: 模拟API调用配置');
try {
  // 导入utils.js中的函数进行测试
  const { getDynamicProxyConfig } = await import('./lib/utils.js');
  
  // 测试静态模式
  process.env.ENABLE_DYNAMIC_REGIONS = 'false';
  console.log('静态模式测试:');
  try {
    const staticConfig = getDynamicProxyConfig();
    console.log('静态配置成功获取');
  } catch (error) {
    console.log('静态配置获取失败:', error.message);
  }
  
  // 测试动态模式
  process.env.ENABLE_DYNAMIC_REGIONS = 'true';
  console.log('动态模式测试:');
  try {
    const dynamicConfig = getDynamicProxyConfig();
    console.log('动态配置成功获取');
    if (dynamicConfig.SELECTED_REGION) {
      console.log(`选中地区: ${dynamicConfig.SELECTED_REGION.countryCode}_${dynamicConfig.SELECTED_REGION.regionCode}_city_${dynamicConfig.SELECTED_REGION.cityCode}`);
    }
  } catch (error) {
    console.log('动态配置获取失败:', error.message);
  }
  
} catch (error) {
  console.log('无法导入utils.js函数:', error.message);
}

console.log('\n🎯 测试建议:');
console.log('1. 如果地区数据为空，请确保 lib/regions.json 文件存在且格式正确');
console.log('2. 如果要启用动态地区功能，请设置 ENABLE_DYNAMIC_REGIONS=true');
console.log('3. 确保代理配置信息完整：PROXY_IP, PROXY_PORT, PROXY_USERNAME, PROXY_PASSWORD');
console.log('4. 动态模式下，每次请求会使用不同的地区，适合需要IP轮换的场景');
console.log('5. 静态模式下，所有请求使用相同的代理配置，适合稳定连接的场景');
