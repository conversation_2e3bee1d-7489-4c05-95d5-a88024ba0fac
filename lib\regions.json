[{"Country Code": "US", "Region Code": "Alabama", "City Code": "Houston"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "Mobile"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "Covington"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "Macon"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "Tuscaloosa"}, {"Country Code": "US", "Region Code": "Alabama", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Houston"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Austin"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "ElPaso"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "LiveOak"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Medina"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Midland"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Victoria"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "Wichita"}, {"Country Code": "US", "Region Code": "Texas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "California", "City Code": "SanBernardino"}, {"Country Code": "US", "Region Code": "California", "City Code": "Fresno"}, {"Country Code": "US", "Region Code": "California", "City Code": "Los<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "California", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "California", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "California", "City Code": "Sacramento"}, {"Country Code": "US", "Region Code": "California", "City Code": "SanDiego"}, {"Country Code": "US", "Region Code": "California", "City Code": "SantaClara"}, {"Country Code": "US", "Region Code": "California", "City Code": "SantaBarbara"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Sandusky"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Ashland"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Athens"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Champaign"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Clermont"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Erie"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Medina"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Miami"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Ottawa"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Portage"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Richland"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Seneca"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Ohio", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Delaware", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "Delaware", "City Code": "NewCastle"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Chesapeake"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "VirginiaBeach"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Augusta"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Bedford"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Bristol"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Brunswick"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Charlotte"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Charlottesville"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Covington"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Danville"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "FallsChurch"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Fredericksburg"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Hanover"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Harrisonburg"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Henrico"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Lynchburg"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Manassas"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Martinsville"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "NewportNews"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Norfolk"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Petersburg"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Portsmouth"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Roanoke"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Salem"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Stafford"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Staunton"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Suffolk"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Waynesboro"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Williamsburg"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "Winchester"}, {"Country Code": "US", "Region Code": "Virginia", "City Code": "York"}, {"Country Code": "US", "Region Code": "Maryland", "City Code": "Baltimore"}, {"Country Code": "US", "Region Code": "Maryland", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "Maryland", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Maryland", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "Maryland", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Maryland", "City Code": "Worcester"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Philadelphia"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "York"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Bedford"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Chester"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Clearfield"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Erie"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Pennsylvania", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Brooklyn"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Fontana"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Glendale"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "GreenBay"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Hawthorne"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Lakewood"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Milwaukee"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Norwalk"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Ontario"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Portage"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Ra<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "RockSprings"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Ashland"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Florence"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "FondduLac"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "LaCrosse"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Lafayette"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Richland"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wisconsin", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Geneva"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Alhambra"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Antioch"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Berkeley"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Burbank"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Chicago"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Cicero"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "ElPaso"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Elizabethtown"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Harrisburg"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Jacksonville"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Jerseyville"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Knoxville"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Lakewood"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Louisville"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Nashville"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Newark"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "OakLawn"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "OrlandPark"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Peoria"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Quincy"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Rockford"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Shelbyville"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Stockton"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Utica"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Wilmington"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Champaign"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Livingston"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Macon"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Richland"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Illinois", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "LehighAcres"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "CapeCoral"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "FortLauderdale"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "FortMyers"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Hawthorne"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Jacksonville"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Memphis"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Miami"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Naples"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Ocala"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Orlando"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Palmdale"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Quincy"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "SanAntonio"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Tampa"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Valparaiso"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Charlotte"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Gadsden"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Hillsborough"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Lafayette"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Pasco"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "SantaRosa"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Florida", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Brooklyn"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Norwalk"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "Hartford"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "NewHaven"}, {"Country Code": "US", "Region Code": "Connecticut", "City Code": "NewLondon"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Boston"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Quincy"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Westminster"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Wilmington"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Bristol"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Norfolk"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Plymouth"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Suffolk"}, {"Country Code": "US", "Region Code": "Massachusetts", "City Code": "Worcester"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "KansasCity"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Minneapolis"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>ley"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Pittsburg"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "SaintPaul"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Stockton"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Cheyenne"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Kingman"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Miami"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Ottawa"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Reno"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Stafford"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "Wichita"}, {"Country Code": "US", "Region Code": "Kansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Beaumont"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Glendora"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Houston"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "LongBeach"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Louisville"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Meridian"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Philadelphia"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Utica"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Covington"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Lafayette"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Mississippi", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alaska", "City Code": "Butte"}, {"Country Code": "US", "Region Code": "Alaska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Alaska", "City Code": "Anchorage"}, {"Country Code": "US", "Region Code": "Alaska", "City Code": "Petersburg"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Glendale"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "LakeHavasuCity"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Mesa"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Miami"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Peoria"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Phoenix"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Tucson"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arizona", "City Code": "Maricopa"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Austin"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Charlotte"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Clarksville"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Fayetteville"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Harrisburg"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Jacksonville"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Knoxville"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Menifee"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Nashville"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Newark"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Omaha"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "SaintPaul"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Texarkana"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Hempstead"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Independence"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Lafayette"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Arkansas", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "ColoradoSprings"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Denver"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Glendale"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Lakewood"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Livermore"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Louisville"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Meridian"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Mesa"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Westminster"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Cheyenne"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Eagle"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "ElPaso"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Pueblo"}, {"Country Code": "US", "Region Code": "Colorado", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "DistrictofColumbia", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Bluffton"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Boston"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Edison"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Fayetteville"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Jacksonville"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Jeffersonville"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Knoxville"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Louisville"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Meridian"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Nashville"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Rome"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "UnionCity"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Camden"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>sy<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Houston"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Macon"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Georgia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Hawaii", "City Code": "Honolulu"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Boise"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Meridian"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Naples"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "<PERSON>ley"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Clearwater"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "TwinFalls"}, {"Country Code": "US", "Region Code": "Idaho", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Austin"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Bluffton"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Boston"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Brooklyn"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Cicero"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Clarksville"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "CrownPoint"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Hagerstown"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Indianapolis"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Jeffersonville"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "LongBeach"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Memphis"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Merrillville"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Nashville"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Portage"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "SaintPaul"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Shelbyville"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "UnionCity"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Upland"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Valparaiso"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Dearborn"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Elkhart"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Huntington"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "LaGrange"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Miami"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Indiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Brooklyn"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Cincinnati"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Clarksville"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Denver"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "GardenGrove"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Knoxville"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Livermore"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Norwalk"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Rockford"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Rome"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "DesMoines"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Dubuque"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Plymouth"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Iowa", "City Code": "Woodbury"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Bardstown"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Beaumont"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Boston"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Elizabethtown"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Glendale"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Irvine"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Jeffersonville"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Livermore"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Louisville"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Providence"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "RussellSprings"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Sacramento"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Shelbyville"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Shepherdsville"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Southgate"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Utica"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Carlisle"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Livingston"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Menifee"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Kentucky", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "NewOrleans"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Lafayette"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Livingston"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Richland"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Louisiana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Detroit"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Naples"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "York"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Oxford"}, {"Country Code": "US", "Region Code": "Maine", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Birmingham"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Brooklyn"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Charlotte"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Clarksville"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Detroit"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Hesper<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Memphis"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Nashville"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>ley"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Portage"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Quincy"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Rockford"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Roseville"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Sandusky"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Shelbyville"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Southgate"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "UnionCity"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Kalamazoo"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Livingston"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Macomb"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Midland"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Muskegon"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Ottawa"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "Saginaw"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Michigan", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "AppleValley"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Austin"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Houston"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Minneapolis"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Rockford"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Roseville"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "SaintPaul"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Clearwater"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Minnesota", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Bellflower"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Berkeley"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Brentwood"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Clarksville"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Glendale"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Harrisburg"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Houston"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Jacksonville"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "KansasCity"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Memphis"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Newark"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Pittsburg"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Pomona"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "SaintPaul"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Shelbyville"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Stockton"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Camden"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Lafayette"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Livingston"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Macon"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "Ozark"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Missouri", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Butte"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Richland"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Montana", "City Code": "Stillwater"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Edison"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Louisville"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Memphis"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Omaha"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "SaintPaul"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Utica"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "York"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Cheyenne"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nebraska", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "Austin"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "Hawthorne"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "LasVegas"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "NorthLasVegas"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "Reno"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Nevada", "City Code": "Eureka"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Brentwood"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "NewHampshire", "City Code": "Hillsborough"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Birmingham"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Edison"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Glendora"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Hawthorne"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "JerseyCity"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Lakewood"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Newark"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Pomona"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Somerset"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Stockton"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "UnionCity"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Burlington"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Camden"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Salem"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Union"}, {"Country Code": "US", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "Albuquerque"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "Corona"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "Crownpoint"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "LasVegas"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "SanAntonio"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "SantaClara"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "SantaFe"}, {"Country Code": "US", "Region Code": "NewMexico", "City Code": "Union"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Atlanta"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Boston"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Brentwood"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Brooklyn"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Cicero"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Clarksville"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Corona"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "CrownPoint"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Denver"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Elizabethtown"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Fayetteville"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Hawthorne"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Jeffersonville"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Lakewood"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Lima"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "LongBeach"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Naples"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Newark"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Ontario"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Philadelphia"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Phoenix"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Pomona"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Rome"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "StatenIsland"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Utica"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Wilmington"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "York"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Erie"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Livingston"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "NewYork"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Queens"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Schenectady"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Seneca"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Suffolk"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NewYork", "City Code": "Westchester"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Durham"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Charlotte"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Denver"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Elizabethtown"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Fayetteville"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Harrisburg"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Jacksonville"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "LongBeach"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Minneapolis"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Nashville"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Wilmington"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Brunswick"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Camden"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>sy<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Lenoir"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Macon"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "Union"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Columbus"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "York"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "Richland"}, {"Country Code": "US", "Region Code": "NorthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Meridian"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Miami"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "OklahomaCity"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Orlando"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "UnionCity"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "G<PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Muskogee"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Ottawa"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Pittsburg"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oklahoma", "City Code": "Tulsa"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Dallas"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Detroit"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Harrisburg"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Jacksonville"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Ontario"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Phoenix"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "SaintPaul"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Oregon", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Middletown"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Providence"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Bristol"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Newport"}, {"Country Code": "US", "Region Code": "RhodeIsland", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Bluffton"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "MyrtleBeach"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Westminster"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "York"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Charleston"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Chester"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Florence"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Georgetown"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Richland"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Spartanburg"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Union"}, {"Country Code": "US", "Region Code": "SouthCarolina", "City Code": "Williamsburg"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Burbank"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Canton"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Harrisburg"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "SiouxFalls"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Aurora"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "FallRiver"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "SouthDakota", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Alexandria"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Antioch"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Brentwood"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Charlotte"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Clarksville"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Decatur"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Fayetteville"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Knoxville"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Lancaster"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Lebanon"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Lexington"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Louisville"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Memphis"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Nashville"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Oakland"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Philadelphia"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Portland"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Rockford"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Shelbyville"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Trenton"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "UnionCity"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Bedford"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Chester"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Cumberland"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Houston"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Macon"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Union"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Tennessee", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "Cleveland"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "Naples"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "<PERSON>ley"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "Portage"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "Providence"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "SaltLakeCity"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "SantaClara"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Utah", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Charlotte"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Concord"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Jeffersonville"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Plainfield"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Richmond"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Westminster"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Wilmington"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Orange"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "Vermont", "City Code": "Windsor"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Arlington"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Burbank"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Dayton"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Fairfield"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Kennewick"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Kent"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Lakewood"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "LongBeach"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Mesa"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Pasco"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Quincy"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Riverside"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Rochester"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Rockford"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Seattle"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Spokane"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Columbia"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Washington", "City Code": "Ya<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Bridgeport"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Buffalo"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Fayetteville"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Glendale"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Greenville"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Madison"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Springfield"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Washington"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON><PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "Raleigh"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "WestVirginia", "City Code": "<PERSON>"}, {"Country Code": "US", "Region Code": "Wyoming", "City Code": "Albany"}, {"Country Code": "US", "Region Code": "Wyoming", "City Code": "Fremont"}, {"Country Code": "US", "Region Code": "Wyoming", "City Code": "HotSprings"}, {"Country Code": "US", "Region Code": "Wyoming", "City Code": "Lincoln"}, {"Country Code": "US", "Region Code": "Wyoming", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "Butte"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "Kimball"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "Marlboro"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Alberta", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Alcona"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Athens"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Baden"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Baltimore"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Bath"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Bayfield"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Burlington"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Caledonia"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Cavan"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Cayuga"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Chatham"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Delaware"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Dorset"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Fingal"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Forest"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Georgetown"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Hillsdale"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Hobart"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Iroquois"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Kearney"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "King"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Lancaster"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "LaSalle"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Leitrim"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Lincoln"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Marathon"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Midland"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Mono"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Niagara"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Paris"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Preston"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "PrinceEdward"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Rockland"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Salem"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Scotland"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Seaforth"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Waterford"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Williamsburg"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Winchester"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Windsor"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Ontario", "City Code": "Wyoming"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Amherst"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Buckingham"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Clermont"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Danville"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Hampstead"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Huntingdon"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Lasalle"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Montcalm"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "<PERSON>ly"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Terrebonne"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Upton"}, {"Country Code": "CA", "Region Code": "Quebec", "City Code": "Windsor"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Chase"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Clearwater"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Delta"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Erie"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Glacier"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Gloucester"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "GrandForks"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Houston"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Lund"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Panorama"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "BritishColumbia", "City Code": "Victoria"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "Austin"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "Dallas"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "LaSalle"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "Miami"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Manitoba", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "Chatham"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "Dorchester"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "Fredericton"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "Hillsborough"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "NewBrunswick", "City Code": "Sussex"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "BayBulls"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "BayRoberts"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "BishopsFalls"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Botwood"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Carbonear"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Clarenville"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "ComeByChance"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "ConceptionBaySouth"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "CornerBrook"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "DeerLake"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Ferm<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Flatrock"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Gambo"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Gander"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Glovertown"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "GrandFallsWindsor"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "HappyValleyGooseBay"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "HareBay"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "LaScie"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "LabradorCity"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Marystown"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Mobile"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Paradise"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Pasadena"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Raleigh"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "SpaniardsBay"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "St.Johns"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Stephenville"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Torbay"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "Trinity"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "NewfoundlandandLabrador", "City Code": "WitlessBay"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "Amherst"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "Eureka"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "Halifax"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "Lunenburg"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "Overton"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "Oxford"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "NovaScotia", "City Code": "Weston"}, {"Country Code": "CA", "Region Code": "PrinceEdwardIsland", "City Code": "Montague"}, {"Country Code": "CA", "Region Code": "PrinceEdwardIsland", "City Code": "Victoria"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Battleford"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Biggar"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "BirchHills"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Broadview"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "BuffaloNarrows"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Canora"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Caronport"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "CarrotRiver"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Cochin"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Eastend"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Eldorado"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Esterhazy"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Estevan"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "FoamLake"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "FortQu<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "GreenLake"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Grenfell"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "HudsonBay"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Humboldt"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "IndianHead"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Kindersley"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "La<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Maidstone"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "MapleCreek"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "MeadowLake"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Mel<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Meota"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Moffat"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "MooseJaw"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Moosomin"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "NorthBattleford"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "NorthPortal"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Outlook"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Oxbow"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Regina"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "ReginaBeach"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Rocanville"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Rosetown"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Saskatoon"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Shellbrook"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Sintaluta"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Spiritwood"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "StonyRapids"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "SwiftCurrent"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Tisdale"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Unity"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Waseca"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Watrous"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "WhiteCity"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Whitewood"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "Wynyard"}, {"Country Code": "CA", "Region Code": "Saskatchewan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CA", "Region Code": "Yukon", "City Code": "Calumet"}, {"Country Code": "CA", "Region Code": "Yukon", "City Code": "Faro"}, {"Country Code": "CA", "Region Code": "Yukon", "City Code": "Mayo"}, {"Country Code": "FR", "Region Code": "Occitanie", "City Code": "Cologne"}, {"Country Code": "FR", "Region Code": "Occitanie", "City Code": "Gironde"}, {"Country Code": "FR", "Region Code": "Occitanie", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FR", "Region Code": "Occitanie", "City Code": "Lasalle"}, {"Country Code": "FR", "Region Code": "Occitanie", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FR", "Region Code": "Occitanie", "City Code": "Montcalm"}, {"Country Code": "FR", "Region Code": "CentreValdeLoire", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FR", "Region Code": "CentreValdeLoire", "City Code": "Luce"}, {"Country Code": "FR", "Region Code": "CentreValdeLoire", "City Code": "Saran"}, {"Country Code": "FR", "Region Code": "HautsdeFrance", "City Code": "Abbeville"}, {"Country Code": "FR", "Region Code": "HautsdeFrance", "City Code": "Clermont"}, {"Country Code": "FR", "Region Code": "HautsdeFrance", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FR", "Region Code": "NouvelleAquitaine", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FR", "Region Code": "NouvelleAquitaine", "City Code": "Landes"}, {"Country Code": "FR", "Region Code": "NouvelleAquitaine", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Arlington"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Banks"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Barbican"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Barnwell"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Barrow"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Bath"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Berkeley"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Broadwater"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Broomfield"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Buckingham"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Carlisle"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Carlton"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Charlton"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Chatham"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Chester"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Chesterfield"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Colchester"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Constantine"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Cotton"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Denver"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Devonport"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Dorchester"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Edgefield"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Falmouth"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Ford"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Gloucester"}, {"Country Code": "GB", "Region Code": "England", "City Code": "GoldenValley"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Halifax"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Hampstead"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Harford"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Hartford"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Hempstead"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Hertford"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Huntingdon"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Huntington"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Ilam"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Kingsbury"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Lancaster"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Launceston"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Leake"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Lincoln"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Litchfield"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Lund"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Maidstone"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "NewYork"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Oxford"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Pennington"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Petersfield"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Pitt"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Portobello"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Preston"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Rock"}, {"Country Code": "GB", "Region Code": "England", "City Code": "SaintJames"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Saint<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Saint<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Sale"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Sedgwick"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Spalding"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Stafford"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Staunton"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Stone"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Throckmorton"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Tolland"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Upton"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Vigo"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Walton"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Ware"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Washington"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Weston"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Winchester"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Windsor"}, {"Country Code": "GB", "Region Code": "England", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Woodbury"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Woodford"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Worcester"}, {"Country Code": "GB", "Region Code": "England", "City Code": "Worth"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Biggar"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Dallas"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Dalmeny"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Georgetown"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Haywood"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Houston"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Jamestown"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Livingston"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Moffat"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Newhaven"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Portobello"}, {"Country Code": "GB", "Region Code": "Scotland", "City Code": "Walton"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "Beaufort"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "Houghton"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "Monmouth"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "Overton"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "Salem"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>ly"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "Valley"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "Victoria"}, {"Country Code": "GB", "Region Code": "Wales", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "Antrim"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "Belfast"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "<PERSON>"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "Granville"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "Hillsborough"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "Kearney"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "Omagh"}, {"Country Code": "GB", "Region Code": "NorthernIreland", "City Code": "Windsor"}, {"Country Code": "DE", "Region Code": "Brandenburg", "City Code": "Karlsruhe"}, {"Country Code": "DE", "Region Code": "Brandenburg", "City Code": "Philadelphia"}, {"Country Code": "DE", "Region Code": "Berlin", "City Code": "Berlin"}, {"Country Code": "DE", "Region Code": "BadenWurttemberg", "City Code": "Amstetten"}, {"Country Code": "DE", "Region Code": "BadenWurttemberg", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "BadenWurttemberg", "City Code": "Karlsruhe"}, {"Country Code": "DE", "Region Code": "BadenWurttemberg", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "BadenWurttemberg", "City Code": "Mengen"}, {"Country Code": "DE", "Region Code": "BadenWurttemberg", "City Code": "<PERSON>"}, {"Country Code": "DE", "Region Code": "BadenWurttemberg", "City Code": "Waldenburg"}, {"Country Code": "DE", "Region Code": "Bremen", "City Code": "Bremen"}, {"Country Code": "DE", "Region Code": "Bremen", "City Code": "Bremerhaven"}, {"Country Code": "DE", "Region Code": "Bremen", "City Code": "Horn"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "Garden"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "Ludwigslust"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "Mecklenburg"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "Rostock"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "Salem"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "Strassen"}, {"Country Code": "DE", "Region Code": "MecklenburgVorpommern", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "BadKreuznach"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Berg"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Betzdorf"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Birkenfeld"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Germersheim"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Kaiserslautern"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "LandauinderPfalz"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "LudwigshafenamRhein"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Mainz"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Nassau"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Salzburg"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "RheinlandPfalz", "City Code": "Worms"}, {"Country Code": "DE", "Region Code": "SchleswigHolstein", "City Code": "Flensburg"}, {"Country Code": "DE", "Region Code": "SchleswigHolstein", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "Saarland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DE", "Region Code": "Saarland", "City Code": "Fischbach"}, {"Country Code": "DE", "Region Code": "Saarland", "City Code": "Neunkirchen"}, {"Country Code": "DE", "Region Code": "Saarland", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "ES", "Region Code": "Galicia", "City Code": "<PERSON>"}, {"Country Code": "ES", "Region Code": "Galicia", "City Code": "Marin"}, {"Country Code": "ES", "Region Code": "Galicia", "City Code": "Real"}, {"Country Code": "ES", "Region Code": "Galicia", "City Code": "Vigo"}, {"Country Code": "ES", "Region Code": "Aragon", "City Code": "Huesca"}, {"Country Code": "ES", "Region Code": "Aragon", "City Code": "Teruel"}, {"Country Code": "ES", "Region Code": "Cantabria", "City Code": "SanSalvador"}, {"Country Code": "ES", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "ES", "Region Code": "Extremadura", "City Code": "Badajoz"}, {"Country Code": "ES", "Region Code": "Extremadura", "City Code": "Caceres"}, {"Country Code": "ES", "Region Code": "Extremadura", "City Code": "Guadalupe"}, {"Country Code": "ES", "Region Code": "Extremadura", "City Code": "LaCumbre"}, {"Country Code": "ES", "Region Code": "Extremadura", "City Code": "SantaMarta"}, {"Country Code": "ES", "Region Code": "Extremadura", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "ES", "Region Code": "Melilla", "City Code": "Melilla"}, {"Country Code": "IT", "Region Code": "Calabria", "City Code": "Filadelfia"}, {"Country Code": "IT", "Region Code": "Calabria", "City Code": "San<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IT", "Region Code": "Campania", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IT", "Region Code": "Campania", "City Code": "Salento"}, {"Country Code": "IT", "Region Code": "Veneto", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IT", "Region Code": "EmiliaRomagna", "City Code": "Bologna"}, {"Country Code": "IT", "Region Code": "EmiliaRomagna", "City Code": "Reno"}, {"Country Code": "IT", "Region Code": "Umbria", "City Code": "Amelia"}, {"Country Code": "IT", "Region Code": "Umbria", "City Code": "Porto"}, {"Country Code": "IT", "Region Code": "Lazio", "City Code": "Rome"}, {"Country Code": "IT", "Region Code": "Lazio", "City Code": "Viterbo"}, {"Country Code": "IT", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON>"}, {"Country Code": "IT", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Toro"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Augusta"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Boulder"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Broadwater"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Brunswick"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Buckingham"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Carlisle"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Midland"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Onslow"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "Rockingham"}, {"Country Code": "AU", "Region Code": "WesternAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "Jamestown"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "SaltLake"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "Virginia"}, {"Country Code": "AU", "Region Code": "SouthAustralia", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Clermont"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Cleveland"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Delta"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Georgetown"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Granville"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Halifax"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Miami"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "PalmBeach"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "RunawayBay"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "SantaBarbara"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Southside"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Texas"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Virginia"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Queensland", "City Code": "Woodford"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Albion"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Arcadia"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Barkly"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Broomfield"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Brunswick"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Burleigh"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Burlington"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Carlton"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Clarendon"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Dallas"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Denver"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Eldorado"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Granite"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "LittleRiver"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Maidstone"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Montmorency"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Newhaven"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Preston"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Rosebud"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "SaintJames"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "Sale"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Victoria", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "Banks"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "City"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "AustralianCapitalTerritory", "City Code": "Weston"}, {"Country Code": "AU", "Region Code": "NorthernTerritory", "City Code": "AliceSprings"}, {"Country Code": "AU", "Region Code": "NorthernTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "NorthernTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "NorthernTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "NorthernTerritory", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "NorthernTerritory", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Cleveland"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Devonport"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "GeorgeTown"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Glenorchy"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Harford"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Hobart"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Latrobe"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Lauderdale"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Launceston"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Paradise"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "<PERSON>"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "SandyBay"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AU", "Region Code": "Tasmania", "City Code": "Wynyard"}, {"Country Code": "AR", "Region Code": "EntreRios", "City Code": "LaPaz"}, {"Country Code": "AR", "Region Code": "EntreRios", "City Code": "SanSalvador"}, {"Country Code": "AR", "Region Code": "EntreRios", "City Code": "SantaAna"}, {"Country Code": "AR", "Region Code": "EntreRios", "City Code": "Victoria"}, {"Country Code": "AR", "Region Code": "EntreRios", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "EntreRios", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "Misiones", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "Misiones", "City Code": "CerroAzul"}, {"Country Code": "AR", "Region Code": "Misiones", "City Code": "Eldorado"}, {"Country Code": "AR", "Region Code": "Misiones", "City Code": "Loreto"}, {"Country Code": "AR", "Region Code": "Misiones", "City Code": "PuertoRico"}, {"Country Code": "AR", "Region Code": "Misiones", "City Code": "SanPedro"}, {"Country Code": "AR", "Region Code": "Misiones", "City Code": "SanVicente"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "BellaVista"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "ElDorado"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "Lima"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "Lincoln"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "Mercedes"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "SanCayetano"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "SanIsidro"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "SanPatricio"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "BuenosAires", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "SantaFe", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "SantaFe", "City Code": "Esperanza"}, {"Country Code": "AR", "Region Code": "SantaFe", "City Code": "<PERSON>es"}, {"Country Code": "AR", "Region Code": "SantaFe", "City Code": "LasRosas"}, {"Country Code": "AR", "Region Code": "SantaFe", "City Code": "MontesdeOca"}, {"Country Code": "AR", "Region Code": "SantaFe", "City Code": "Rosario"}, {"Country Code": "AR", "Region Code": "SantaFe", "City Code": "VillaConstitucion"}, {"Country Code": "AR", "Region Code": "Chaco", "City Code": "CampoLargo"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Bandera"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "NuevaEsperanza"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "SanPedro"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "SanPedro"}, {"Country Code": "AR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "SantaClara"}, {"Country Code": "AR", "Region Code": "Salta", "City Code": "Angostura"}, {"Country Code": "AR", "Region Code": "Salta", "City Code": "ElCarmen"}, {"Country Code": "AR", "Region Code": "RioNegro", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "RioNegro", "City Code": "<PERSON>"}, {"Country Code": "AR", "Region Code": "Neuquen", "City Code": "Barrancas"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "Concepcion"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "LaCruz"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "Libertad"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "Mercedes"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "San<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "SanMiguel"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "SantaLucia"}, {"Country Code": "AR", "Region Code": "Corrientes", "City Code": "Sauce"}, {"Country Code": "AR", "Region Code": "LaRioja", "City Code": "LaRioja"}, {"Country Code": "AR", "Region Code": "SanJuan", "City Code": "SanJuan"}, {"Country Code": "AR", "Region Code": "Catamarca", "City Code": "Belen"}, {"Country Code": "AR", "Region Code": "Catamarca", "City Code": "SanAntonio"}, {"Country Code": "AR", "Region Code": "LaPampa", "City Code": "SantaIsabel"}, {"Country Code": "AR", "Region Code": "LaPampa", "City Code": "SantaTeresa"}, {"Country Code": "MX", "Region Code": "Jalisco", "City Code": "Guadalajara"}, {"Country Code": "MX", "Region Code": "Jalisco", "City Code": "SanMarcos"}, {"Country Code": "MX", "Region Code": "Jalisco", "City Code": "SanPatricio"}, {"Country Code": "MX", "Region Code": "Sonora", "City Code": "Moctezuma"}, {"Country Code": "MX", "Region Code": "Sonora", "City Code": "Providencia"}, {"Country Code": "MX", "Region Code": "Sonora", "City Code": "SantaAna"}, {"Country Code": "MX", "Region Code": "Guanajuato", "City Code": "Aldama"}, {"Country Code": "MX", "Region Code": "Guanajuato", "City Code": "<PERSON>"}, {"Country Code": "MX", "Region Code": "Guanajuato", "City Code": "Salamanca"}, {"Country Code": "MX", "Region Code": "Guanajuato", "City Code": "SantaTeresa"}, {"Country Code": "MX", "Region Code": "Guanajuato", "City Code": "Victoria"}, {"Country Code": "MX", "Region Code": "Puebla", "City Code": "Esperanza"}, {"Country Code": "MX", "Region Code": "Puebla", "City Code": "LaCeiba"}, {"Country Code": "MX", "Region Code": "Puebla", "City Code": "Sonora"}, {"Country Code": "MX", "Region Code": "Nayarit", "City Code": "SanVicente"}, {"Country Code": "MX", "Region Code": "Sinaloa", "City Code": "Angostura"}, {"Country Code": "MX", "Region Code": "Sinaloa", "City Code": "CostaRica"}, {"Country Code": "MX", "Region Code": "Sinaloa", "City Code": "ElDorado"}, {"Country Code": "MX", "Region Code": "Sinaloa", "City Code": "ElRosario"}, {"Country Code": "MX", "Region Code": "Sinaloa", "City Code": "LaCruz"}, {"Country Code": "MX", "Region Code": "Durango", "City Code": "SantaClara"}, {"Country Code": "MX", "Region Code": "Tabasco", "City Code": "Belen"}, {"Country Code": "MX", "Region Code": "Oaxaca", "City Code": "SanDiego"}, {"Country Code": "MX", "Region Code": "Chihuahua", "City Code": "Ascension"}, {"Country Code": "MX", "Region Code": "Chihuahua", "City Code": "Buenaventura"}, {"Country Code": "MX", "Region Code": "Chihuahua", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Chihuahua", "City Code": "SantaBarbara"}, {"Country Code": "MX", "Region Code": "Aguascalientes", "City Code": "Aguascalientes"}, {"Country Code": "MX", "Region Code": "Aguascalientes", "City Code": "Asientos"}, {"Country Code": "MX", "Region Code": "Aguascalientes", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Aguascalientes", "City Code": "Ojocaliente"}, {"Country Code": "MX", "Region Code": "Aguascalientes", "City Code": "PaloAlto"}, {"Country Code": "MX", "Region Code": "Aguascalientes", "City Code": "SanFranciscodelosRomo"}, {"Country Code": "MX", "Region Code": "BajaCalifornia", "City Code": "Delta"}, {"Country Code": "MX", "Region Code": "BajaCalifornia", "City Code": "SantaIsabel"}, {"Country Code": "MX", "Region Code": "BajaCalifornia", "City Code": "Tecate"}, {"Country Code": "MX", "Region Code": "BajaCalifornia", "City Code": "Tijuana"}, {"Country Code": "MX", "Region Code": "BajaCaliforniaSur", "City Code": "LaPaz"}, {"Country Code": "MX", "Region Code": "BajaCaliforniaSur", "City Code": "Loreto"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "FronteraComalapa"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "LaConcordia"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "La<PERSON>iber<PERSON>"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "LaTrinitaria"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "LasRosas"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "Oxchuc"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "Pichucalco"}, {"Country Code": "MX", "Region Code": "Chiapas", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Colima", "City Code": "Manzanillo"}, {"Country Code": "MX", "Region Code": "Hidalgo", "City Code": "Atitalaquia"}, {"Country Code": "MX", "Region Code": "Hidalgo", "City Code": "ElLlano"}, {"Country Code": "MX", "Region Code": "Hidalgo", "City Code": "HuejutladeReyes"}, {"Country Code": "MX", "Region Code": "Hidalgo", "City Code": "PachucadeSoto"}, {"Country Code": "MX", "Region Code": "Hidalgo", "City Code": "SanMarcos"}, {"Country Code": "MX", "Region Code": "<PERSON><PERSON>", "City Code": "Axochiapan"}, {"Country Code": "MX", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "<PERSON><PERSON>", "City Code": "Cuernavaca"}, {"Country Code": "MX", "Region Code": "<PERSON><PERSON>", "City Code": "Jiutepec"}, {"Country Code": "MX", "Region Code": "<PERSON><PERSON>", "City Code": "Mazatepec"}, {"Country Code": "MX", "Region Code": "QuintanaRoo", "City Code": "<PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "Aldama"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "Altamira"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "ElMante"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "Jaumave"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "NuevoLaredo"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "Reynosa"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "SanJuanito"}, {"Country Code": "MX", "Region Code": "Tamaulip<PERSON>", "City Code": "Tampico"}, {"Country Code": "MX", "Region Code": "Tlaxcala", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Tlaxcala", "City Code": "NanacamilpadeMarianoArista"}, {"Country Code": "MX", "Region Code": "Tlaxcala", "City Code": "Tlaxcala"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Chalchihuites"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "ElSalvador"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Guadalupe"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Ju<PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Loreto"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Miguel<PERSON><PERSON>"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Momax"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "MonteEscobedo"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "MoyahuadeEs<PERSON>da"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Pinos"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "SainAlto"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Sombrerete"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Tabasco"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Trancoso"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "VilladeCos"}, {"Country Code": "MX", "Region Code": "Zacatecas", "City Code": "Zacatecas"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "Acegua"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "Loreto"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "Minas"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "SantaTecla"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "Sarandi"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "Soledade"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "Tapejara"}, {"Country Code": "BR", "Region Code": "RioGrandedoSul", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "RiodeJaneiro", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MinasGerais", "City Code": "Cassia"}, {"Country Code": "BR", "Region Code": "MinasGerais", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MinasGerais", "City Code": "SantaBarbara"}, {"Country Code": "BR", "Region Code": "MinasGerais", "City Code": "Virginia"}, {"Country Code": "BR", "Region Code": "Tocantins", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Tocantins", "City Code": "Palmas"}, {"Country Code": "BR", "Region Code": "Tocantins", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Pernambuco", "City Code": "BomJardim"}, {"Country Code": "BR", "Region Code": "Pernambuco", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Pernambuco", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Pernambuco", "City Code": "Paulista"}, {"Country Code": "BR", "Region Code": "Pernambuco", "City Code": "Recife"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "AltoAlegre"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "Are<PERSON>"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "Bananal"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "Boa<PERSON>ora"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "B<PERSON>jo<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "Can<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "Una"}, {"Country Code": "BR", "Region Code": "Bahia", "City Code": "Valverde"}, {"Country Code": "BR", "Region Code": "Alagoas", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Alagoas", "City Code": "Capela"}, {"Country Code": "BR", "Region Code": "Alagoas", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "SantaCatarina", "City Code": "Bananal"}, {"Country Code": "BR", "Region Code": "SantaCatarina", "City Code": "Campinas"}, {"Country Code": "BR", "Region Code": "SantaCatarina", "City Code": "CerroAzul"}, {"Country Code": "BR", "Region Code": "SantaCatarina", "City Code": "Cocal"}, {"Country Code": "BR", "Region Code": "SantaCatarina", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "SantaCatarina", "City Code": "Turvo"}, {"Country Code": "BR", "Region Code": "RioGrandedoNorte", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "RioGrandedoNorte", "City Code": "PedraPreta"}, {"Country Code": "BR", "Region Code": "Acre", "City Code": "AltoAlegre"}, {"Country Code": "BR", "Region Code": "Acre", "City Code": "PortoWalter"}, {"Country Code": "BR", "Region Code": "Acre", "City Code": "RioBranco"}, {"Country Code": "BR", "Region Code": "Acre", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "Acre", "City Code": "Senador<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Amambai"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "AparecidadoTaboado"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Bananal"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "BelaVista"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "CostaRica"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Eldorado"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "GuiaLopesdaLaguna"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Iguatemi"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Maracaju"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Paranhos"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "PortoMurtin<PERSON>"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "RibasdoRioPardo"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "RioBrilhante"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "RioVerdedeMatoGrosso"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Tacuru"}, {"Country Code": "BR", "Region Code": "MatoGrossodoSul", "City Code": "Terenos"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "AltaFloresta"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "AltoAraguaia"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "AltoParaguai"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Araguaiana"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Araputanga"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "BarradoBugres"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Caceres"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "CampoNovodoParecis"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Diaman<PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Jaciara"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "LucasdoRioVerde"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "MirassoldOeste"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Nobres"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "NovaXavantina"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Ouro<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "PedraPreta"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "PonteseLacerda"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "PortoEstrela"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "SantaTerezinha"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Sinop"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "VilaRica"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Aracaju"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "CampodoBrito"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "FreiPaulo"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Itabaiana"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Itaporangad<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Laran<PERSON><PERSON><PERSON>"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "MonteAlegredeSergipe"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "NossaSenhoradasDores"}, {"Country Code": "BR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "PortodaFolha"}, {"Country Code": "TR", "Region Code": "Trabzon", "City Code": "Of"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Hat<PERSON>", "City Code": "Belen"}, {"Country Code": "TR", "Region Code": "B<PERSON>cik", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "Tatvan"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "Gerede"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "Mu<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Bucak"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Bursa", "City Code": "Gemlik"}, {"Country Code": "TR", "Region Code": "Bursa", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Bursa", "City Code": "Kestel"}, {"Country Code": "TR", "Region Code": "Bursa", "City Code": "Mudanya"}, {"Country Code": "TR", "Region Code": "Canakkale", "City Code": "Biga"}, {"Country Code": "TR", "Region Code": "Canakkale", "City Code": "Bozcaada"}, {"Country Code": "TR", "Region Code": "Canakkale", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Canakkale", "City Code": "Gelibolu"}, {"Country Code": "TR", "Region Code": "Canakkale", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Canakkale", "City Code": "Yenice"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Bekill<PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Bozkurt"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Konak"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Ed<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Ed<PERSON><PERSON>", "City Code": "Havsa"}, {"Country Code": "TR", "Region Code": "Erzincan", "City Code": "Tercan"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Gaziantep", "City Code": "Nizip"}, {"Country Code": "TR", "Region Code": "Gaziantep", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Espiye"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Piraziz"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Tirebolu"}, {"Country Code": "TR", "Region Code": "Afyonkarahisar", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Afyonkarahisar", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Mersin", "City Code": "Anamur"}, {"Country Code": "TR", "Region Code": "Mersin", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Mersin", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Mersin", "City Code": "Silifke"}, {"Country Code": "TR", "Region Code": "Mersin", "City Code": "Tarsus"}, {"Country Code": "TR", "Region Code": "Kastamonu", "City Code": "Bozkurt"}, {"Country Code": "TR", "Region Code": "Kastamonu", "City Code": "Cide"}, {"Country Code": "TR", "Region Code": "Kastamonu", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Kastamonu", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Incesu"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Kocasinan"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Melikgazi"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Konya", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Konya", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Konya", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Konya", "City Code": "Se<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Malatya", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Malatya", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "Ortaca"}, {"Country Code": "TR", "Region Code": "Ordu", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Ordu", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Rize", "City Code": "Pazar"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Geyve"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Hendek"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Kaynarca"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Sapanca"}, {"Country Code": "TR", "Region Code": "Siirt", "City Code": "Baykan"}, {"Country Code": "TR", "Region Code": "Siirt", "City Code": "Siirt"}, {"Country Code": "TR", "Region Code": "Sinop", "City Code": "Boyabat"}, {"Country Code": "TR", "Region Code": "Sinop", "City Code": "Dikmen"}, {"Country Code": "TR", "Region Code": "Sinop", "City Code": "Erfelek"}, {"Country Code": "TR", "Region Code": "Sinop", "City Code": "Sinop"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "Gemerek"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Ankara", "City Code": "Akyurt"}, {"Country Code": "TR", "Region Code": "Ankara", "City Code": "Beypazari"}, {"Country Code": "TR", "Region Code": "Ankara", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Ankara", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Ankara", "City Code": "Mamak"}, {"Country Code": "TR", "Region Code": "Ankara", "City Code": "Sincan"}, {"Country Code": "TR", "Region Code": "Tokat", "City Code": "Pazar"}, {"Country Code": "TR", "Region Code": "Tokat", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Tokat", "City Code": "Zile"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Mazgirt"}, {"Country Code": "TR", "Region Code": "<PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Yozgat", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Zonguldak", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "Zonguldak", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "<PERSON>", "City Code": "Kozluk"}, {"Country Code": "TR", "Region Code": "<PERSON>", "City Code": "Sason"}, {"Country Code": "TR", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TR", "Region Code": "O<PERSON>i<PERSON>", "City Code": "Toprakkale"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Anser<PERSON>ue<PERSON>"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Buenaventura"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Bugalagrande"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Caicedonia"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Cali"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Cartago"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Dagua"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "ElDovio"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Florida"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Ginebra"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "GuadalajaradeBuga"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "LaVictoria"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Obando"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Restrepo"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Sevilla"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Toro"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Versalles"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Yotoco"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Yumbo"}, {"Country Code": "CO", "Region Code": "ValledelCauca", "City Code": "Zarzal"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "Angostura"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "Caceres"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "Carolina"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "Guadalupe"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "LosAndes"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "Rionegro"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "SanVicente"}, {"Country Code": "CO", "Region Code": "Antioquia", "City Code": "SantaBarbara"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Cajamarca"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Casabianca"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Chaparral"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "C<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Espinal"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Fresno"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Guamo"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Guayabal"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Herveo"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Honda"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Ortega"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Planadas"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "ValledeSanJuan"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Tolima", "City Code": "Villarrica"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "Abrego"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "Durania"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "ElCarmen"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "Gramalote"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "LaEsperanza"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "LaPlaya"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "Labateca"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "LosPatios"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "Pamplona"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "PuertoSantander"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "SanCalixto"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "Sardinata"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "Silos"}, {"Country Code": "CO", "Region Code": "NortedeSantander", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "Algarrobo"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "Aracataca"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "ElBanco"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "Guamal"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "NuevaGranada"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "<PERSON>"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "Puebloviejo"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "Remolino"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "Salamina"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "SanAntonio"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "SantaAna"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "SantaMarta"}, {"Country Code": "CO", "Region Code": "Magdalena", "City Code": "Tenerife"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Aratoca"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Barrancabermeja"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Bucaramanga"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Capitanejo"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Cimitarra"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Concepcion"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "ElCarmen"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Floridablanca"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Guadalupe"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "LaBelleza"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "LaPaz"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Mogotes"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Molag<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Ocamonte"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Oiba"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Piedecuesta"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "PuenteNacional"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "PuertoParra"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "PuertoWilches"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "SabanadeTorres"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "SanGil"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Simacota"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "Socorro"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Santander", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "Anserma"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "Balboa"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "LaVirginia"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "<PERSON>"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "PuebloRico"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "SantaRosadeCabal"}, {"Country Code": "CO", "Region Code": "Risaralda", "City Code": "Santuario"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Aguadas"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Filadelfia"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "LaDorada"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Man<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Marquetalia"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Norcasia"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Pensilvania"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Risaralda"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Salamina"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Victoria"}, {"Country Code": "CO", "Region Code": "Caldas", "City Code": "Viterbo"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Aguachica"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Be<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Bosconia"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Chimichagua"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "ElCopey"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "ElPaso"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Gamarra"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "LaJaguadeIbirico"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Pelaya"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "SanDiego"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Tamalameque"}, {"Country Code": "CO", "Region Code": "Cesar", "City Code": "Valledupar"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "CastillaLaNueva"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "Cumaral"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "ElCastillo"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "FuentedeOro"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "Guamal"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "Mesetas"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "PuertoConcordia"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "PuertoLleras"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "PuertoRico"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "SanCarlosde<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "SanJuandeArama"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "Villavice<PERSON><PERSON>"}, {"Country Code": "CO", "Region Code": "Meta", "City Code": "VistaHermosa"}, {"Country Code": "ZA", "Region Code": "WesternCape", "City Code": "<PERSON>"}, {"Country Code": "ZA", "Region Code": "WesternCape", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "ZA", "Region Code": "WesternCape", "City Code": "Riversdale"}, {"Country Code": "ZA", "Region Code": "WesternCape", "City Code": "<PERSON>"}, {"Country Code": "ZA", "Region Code": "WesternCape", "City Code": "Worcester"}, {"Country Code": "ZA", "Region Code": "FreeState", "City Code": "Virginia"}, {"Country Code": "ZA", "Region Code": "Mpumalanga", "City Code": "Belfast"}, {"Country Code": "ZA", "Region Code": "Mpumalanga", "City Code": "Carolina"}, {"Country Code": "ZA", "Region Code": "NorthernCape", "City Code": "<PERSON><PERSON>"}, {"Country Code": "ZA", "Region Code": "NorthernCape", "City Code": "<PERSON>"}, {"Country Code": "CL", "Region Code": "Atacama", "City Code": "Algarrobo"}, {"Country Code": "TH", "Region Code": "SuratThani", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Sangkhom"}, {"Country Code": "TH", "Region Code": "NakhonSiThammarat", "City Code": "ChaloemPhraKiat"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "LamLukKa"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "LatLumKaeo"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON>g<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Thanyaburi"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "PaMok"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "SamKo"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "Ban<PERSON>i"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "KhokSamrong"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "LamSonthi"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "PhatthanaNik<PERSON>"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "SaBot"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lopburi", "City Code": "ThaWung"}, {"Country Code": "TH", "Region Code": "SingBuri", "City Code": "InBuri"}, {"Country Code": "TH", "Region Code": "SingBuri", "City Code": "PhromBuri"}, {"Country Code": "TH", "Region Code": "ChaiNat", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "ChaiNat", "City Code": "Sankhaburi"}, {"Country Code": "TH", "Region Code": "ChaiNat", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "BanMo"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "ChaloemPhraKiat"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "KaengKhoi"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "MuakLek"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "PhraPhutthabat"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Saraburi", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "BanChang"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "PluakDaeng"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "KhaoKhitcha<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "K<PERSON>ung"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "LaemSing"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "NaYaiAm"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "PongNamRon"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "So<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Trat", "City Code": "BoRai"}, {"Country Code": "TH", "Region Code": "Trat", "City Code": "KhaoSaming"}, {"Country Code": "TH", "Region Code": "Trat", "City Code": "KhlongYai"}, {"Country Code": "TH", "Region Code": "Trat", "City Code": "KoKut"}, {"Country Code": "TH", "Region Code": "Trat", "City Code": "LaemNgop"}, {"Country Code": "TH", "Region Code": "Chachoengsao", "City Code": "BanPho"}, {"Country Code": "TH", "Region Code": "Chachoengsao", "City Code": "BangKhla"}, {"Country Code": "TH", "Region Code": "Chachoengsao", "City Code": "BangPakong"}, {"Country Code": "TH", "Region Code": "Chachoengsao", "City Code": "PhanomSarakham"}, {"Country Code": "TH", "Region Code": "Chachoengsao", "City Code": "PlaengYao"}, {"Country Code": "TH", "Region Code": "Chachoengsao", "City Code": "SanamChaiKhet"}, {"Country Code": "TH", "Region Code": "Chachoengsao", "City Code": "ThaTakiap"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "SiMahaPhot"}, {"Country Code": "TH", "Region Code": "NakhonNayok", "City Code": "BanNa"}, {"Country Code": "TH", "Region Code": "NakhonNayok", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "NakhonNayok", "City Code": "Pak<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "BanDan"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "BanKruat"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "BanMaiChaiyaphot"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "ChaloemPhraKiat"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "LamPlaiMat"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Nang<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "NongKi"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "PrakhonChai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "ChomPhra"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "ChumphonBuri"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "Lamduan"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "PhanomDongRak"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "Rattanaburi"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "SiKhoraphum"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "SiNarong"}, {"Country Code": "TH", "Region Code": "Surin", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "DetUdom"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "LaoSueaKok"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "NaYia"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "SiMueangMai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "TanSum"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "ThungSiUdom"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "WarinChamrap"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "KutChum"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "LoengNokTha"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "PaTio"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "ThaiCharoen"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "BamnetNarong"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "KaengKhro"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "NoenSanga"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "NongBuaRawe"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "PhakdiChumphon"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chaiyaphum", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "BuengKan", "City Code": "BuengKhongLong"}, {"Country Code": "TH", "Region Code": "BuengKan", "City Code": "PhonCharoen"}, {"Country Code": "TH", "Region Code": "NongBuaLamphu", "City Code": "NaKlang"}, {"Country Code": "TH", "Region Code": "NongBuaLamphu", "City Code": "NaWang"}, {"Country Code": "TH", "Region Code": "NongBuaLamphu", "City Code": "SiBunRueang"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "BanFang"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "BanHaet"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "BanPhai"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "ChumPhae"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "Kranuan"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "Nong<PERSON>ue<PERSON>"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "<PERSON>ra<PERSON>uen"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "PueaiNoi"}, {"Country Code": "TH", "Region Code": "K<PERSON><PERSON><PERSON>", "City Code": "SiChomphu"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "BanDung"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Chaiwan"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "NamSom"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "NonSaat"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON>g<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "SangKhom"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "ThungFon"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "WangSamMo"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "Dan<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "NongHin"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "PakChom"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "Ph<PERSON><PERSON><PERSON>ueng"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "WangSaphung"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>P<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "KutRang"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Wapi<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "RoiEt", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "RoiEt", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "RoiEt", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "RoiEt", "City Code": "NongHi"}, {"Country Code": "TH", "Region Code": "RoiEt", "City Code": "PathumRat"}, {"Country Code": "TH", "Region Code": "RoiEt", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Khong<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Kuchinarai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "NongKungSi"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Akat<PERSON><PERSON>nuai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>en<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "KutBak"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "PhonNaKaeo"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "WanonNi<PERSON>"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "NaWa"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "PlaPak"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "SiSongkhram"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "ThatPhanom"}, {"Country Code": "TH", "Region Code": "NakhonPhanom", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "NikhomKhamSoi"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Li"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "MaeTha"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON>hung<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "ChaeHom"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "HangChat"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "MaeMo"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "MaePhrik"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "MaeTha"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "SoemNgam"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Lampang", "City Code": "Wang<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "ThaPla"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Tron"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "DokKhamTai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "MaeChai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Pong"}, {"Country Code": "TH", "Region Code": "MaeHongSon", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "BanRai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "LanSak"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "KhlongKhlung"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "KhlongLan"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "KosamphiNakhon"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "Lan<PERSON>rab<PERSON>"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "KamphaengPhet", "City Code": "SaiNgam"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "BanDanLanHoi"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "BanNa"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "KongKrailat"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "Sawankhalok"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Sukhothai", "City Code": "ThungSalia<PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Bang<PERSON><PERSON><PERSON>ak"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "BuengNaRang"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "DongCharoen"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "SakLek"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "BuengSamPhan"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "LomKao"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "LomSak"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phetchabun", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "BanKha"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "BanPong"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "BangPhae"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "ChomBueng"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "DamnoenSaduak"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "Photharam"}, {"Country Code": "TH", "Region Code": "Ratchaburi", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "NakhonPathom", "City Code": "BangLen"}, {"Country Code": "TH", "Region Code": "NakhonPathom", "City Code": "DonTum"}, {"Country Code": "TH", "Region Code": "NakhonPathom", "City Code": "KamphaengSaen"}, {"Country Code": "TH", "Region Code": "NakhonPathom", "City Code": "NakhonChaiSi"}, {"Country Code": "TH", "Region Code": "NakhonPathom", "City Code": "SamPhran"}, {"Country Code": "TH", "Region Code": "SamutSakhon", "City Code": "BanPhaeo"}, {"Country Code": "TH", "Region Code": "SamutSakhon", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON>ut<PERSON><PERSON>", "City Code": "Amphawa"}, {"Country Code": "TH", "Region Code": "Phetchaburi", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phetchaburi", "City Code": "KaengKrachan"}, {"Country Code": "TH", "Region Code": "Phetchaburi", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phetchaburi", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phetchaburi", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Prachuap<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Prachuap<PERSON><PERSON><PERSON><PERSON>", "City Code": "Bang<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Prachuap<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Prachuap<PERSON><PERSON><PERSON><PERSON>", "City Code": "KuiBuri"}, {"Country Code": "TH", "Region Code": "Prachuap<PERSON><PERSON><PERSON><PERSON>", "City Code": "PranBuri"}, {"Country Code": "TH", "Region Code": "Prachuap<PERSON><PERSON><PERSON><PERSON>", "City Code": "SamRoiYot"}, {"Country Code": "TH", "Region Code": "Prachuap<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "AoLuek"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "KhlongThom"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "KoLanta"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "NueaKhlong"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "PhangNga", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "PhangNga", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "PhangNga", "City Code": "TakuaPa"}, {"Country Code": "TH", "Region Code": "PhangNga", "City Code": "Ta<PERSON><PERSON><PERSON><PERSON>g"}, {"Country Code": "TH", "Region Code": "PhangNga", "City Code": "Thai<PERSON>ueang"}, {"Country Code": "TH", "Region Code": "PhangNga", "City Code": "ThapPut"}, {"Country Code": "TH", "Region Code": "Chumphon", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chumphon", "City Code": "LangSuan"}, {"Country Code": "TH", "Region Code": "Chumphon", "City Code": "Pathio"}, {"Country Code": "TH", "Region Code": "Chumphon", "City Code": "Phato"}, {"Country Code": "TH", "Region Code": "Chumphon", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Chumphon", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Satun", "City Code": "KhuanDon"}, {"Country Code": "TH", "Region Code": "Satun", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Satun", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phatthalung", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "Phatthalung", "City Code": "KongRa"}, {"Country Code": "TH", "Region Code": "Phatthalung", "City Code": "SiBanphot"}, {"Country Code": "TH", "Region Code": "Phatthalung", "City Code": "Srinagarindra"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "KhokPho"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "Mayo"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "NongChik"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "Panare"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "SaiBuri"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "KaBang"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "KrongPiNang"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "ThanTo"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "SiSakhon"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "SungaiKolok"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "TakBai"}, {"Country Code": "TH", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "MY", "Region Code": "Sabah", "City Code": "Beaufort"}, {"Country Code": "MY", "Region Code": "Terengganu", "City Code": "KualaTerengganu"}, {"Country Code": "MY", "Region Code": "Terengganu", "City Code": "<PERSON><PERSON>"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "KualaPilah"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON>", "City Code": "Kampar"}, {"Country Code": "MY", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "WestBengal", "City Code": "Gazipur"}, {"Country Code": "IN", "Region Code": "WestBengal", "City Code": "Kishanganj"}, {"Country Code": "IN", "Region Code": "WestBengal", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Karnataka", "City Code": "Bijapur"}, {"Country Code": "IN", "Region Code": "Rajasthan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Rajasthan", "City Code": "Ramgarh"}, {"Country Code": "IN", "Region Code": "Maharashtra", "City Code": "Aurangabad"}, {"Country Code": "IN", "Region Code": "Maharashtra", "City Code": "Bembla"}, {"Country Code": "IN", "Region Code": "MadhyaPradesh", "City Code": "Bagh"}, {"Country Code": "IN", "Region Code": "MadhyaPradesh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "MadhyaPradesh", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "MadhyaPradesh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Haryana", "City Code": "Bilaspur"}, {"Country Code": "IN", "Region Code": "Assam", "City Code": "Dimapur"}, {"Country Code": "IN", "Region Code": "UttarPradesh", "City Code": "Balrampur"}, {"Country Code": "IN", "Region Code": "UttarPradesh", "City Code": "Faridpur"}, {"Country Code": "IN", "Region Code": "UttarPradesh", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "UttarPradesh", "City Code": "Hamirpur"}, {"Country Code": "IN", "Region Code": "UttarPradesh", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Gujarat", "City Code": "Una"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Araria"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Aurangabad"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Banka"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Be<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Bhagalpur"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Dhaka"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Gopalganj"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Jamalpur"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Khagaria"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Muzaffarpur"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Purnia"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "Samastipur"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Bihar", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Balod"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "BalodaBazar"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Bilaspur"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Kondagaon"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Malkangiri"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Raigarh"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Raipur"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Rajnandgaon"}, {"Country Code": "IN", "Region Code": "Chhattisgarh", "City Code": "Sakti"}, {"Country Code": "IN", "Region Code": "Chandigarh", "City Code": "Chandigarh"}, {"Country Code": "IN", "Region Code": "DadraandNagarHaveliandDamanandDiu", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "DadraandNagarHaveliandDamanandDiu", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "Bilaspur"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "Chamba"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "Hamirpur"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "Mandi"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "HimachalPradesh", "City Code": "Una"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "Chatra"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "Dhanbad"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "Hazaribagh"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "Jamtara"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "Latehar"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "Ranchi"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "<PERSON>"}, {"Country Code": "IN", "Region Code": "Jharkhand", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "Jammu"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "Kathua"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "Samba"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "Srinagar"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "Udhampur"}, {"Country Code": "IN", "Region Code": "JammuandKashmir", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Manipur", "City Code": "Churachandpur"}, {"Country Code": "IN", "Region Code": "Manipur", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Manipur", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Mizoram", "City Code": "Aizawl"}, {"Country Code": "IN", "Region Code": "Mizoram", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Mizoram", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Mizoram", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Mizoram", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Mizoram", "City Code": "Serchhip"}, {"Country Code": "IN", "Region Code": "Nagaland", "City Code": "Kohima"}, {"Country Code": "IN", "Region Code": "Nagaland", "City Code": "Mokokchung"}, {"Country Code": "IN", "Region Code": "Nagaland", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Balangir"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Bargarh"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Deogarh"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Ganjam"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Jharsuguda"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Ko<PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Nayagarh"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Sambalpur"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Sonepur"}, {"Country Code": "IN", "Region Code": "Odisha", "City Code": "Sundargarh"}, {"Country Code": "IN", "Region Code": "Puducherry", "City Code": "Karaikal"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Chengalpattu"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Chennai"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Coimbatore"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Cuddalore"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Dharmapuri"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Dindigul"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Kanchipuram"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Krishnagiri"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Madurai"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Mayiladuthurai"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Nagapattinam"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Namakkal"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Pudukkottai"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Salem"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Sivaganga"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Thanjavur"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Tiruchirappalli"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Tirunelveli"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Tiruvannamalai"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Tamil<PERSON><PERSON><PERSON>", "City Code": "Villupuram"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "Almora"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "NainiTal"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "Pithoragarh"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "Srinagar"}, {"Country Code": "IN", "Region Code": "Uttarakhand", "City Code": "Uttarkashi"}, {"Country Code": "ID", "Region Code": "Bali", "City Code": "Karangasem"}, {"Country Code": "ID", "Region Code": "Jambi", "City Code": "Jambi"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Dhaka"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Faridpur"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Gazipur"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Jamalpur"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Madaripur"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Mymensingh"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Narayanganj"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Narsingdi"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Netrakona"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BD", "Region Code": "Dhaka", "City Code": "Tangail"}, {"Country Code": "BD", "Region Code": "Khulna", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BD", "Region Code": "Khulna", "City Code": "Khulna"}, {"Country Code": "BD", "Region Code": "Khulna", "City Code": "Kushtia"}, {"Country Code": "BD", "Region Code": "Khulna", "City Code": "Narail"}, {"Country Code": "BD", "Region Code": "Khulna", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BD", "Region Code": "<PERSON>yl<PERSON><PERSON>", "City Code": "Habiganj"}, {"Country Code": "BD", "Region Code": "<PERSON>yl<PERSON><PERSON>", "City Code": "<PERSON>yl<PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "Kyoto", "City Code": "Ido"}, {"Country Code": "JP", "Region Code": "Hokkaido", "City Code": "<PERSON>"}, {"Country Code": "JP", "Region Code": "Niigata", "City Code": "Agano"}, {"Country Code": "JP", "Region Code": "Niigata", "City Code": "Minamiuo<PERSON>a"}, {"Country Code": "JP", "Region Code": "Niigata", "City Code": "<PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "Niigata", "City Code": "Uonuma"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Kanazawa"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "Iga"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "Suzuka"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "Yokkaichi"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "Wake"}, {"Country Code": "JP", "Region Code": "Tokushima", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Uwajima"}, {"Country Code": "JP", "Region Code": "Saga", "City Code": "Kashima"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "JP", "Region Code": "Kagoshima", "City Code": "China"}, {"Country Code": "JP", "Region Code": "Fukushima", "City Code": "<PERSON><PERSON>"}, {"Country Code": "PK", "Region Code": "GilgitBaltistan", "City Code": "Gilgit"}, {"Country Code": "PK", "Region Code": "GilgitBaltistan", "City Code": "Skardu"}, {"Country Code": "PK", "Region Code": "Islamabad", "City Code": "Islamabad"}, {"Country Code": "UZ", "Region Code": "Toshkent", "City Code": "Tashkent"}, {"Country Code": "PY", "Region Code": "Amam<PERSON>", "City Code": "BellaVista"}, {"Country Code": "PY", "Region Code": "Amam<PERSON>", "City Code": "CapitanBado"}, {"Country Code": "PY", "Region Code": "Amam<PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "PY", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "PY", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "Altos"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "ArroyosyEsteros"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "Atyra"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "Caacupe"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "Emboscada"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "EusebioAyala"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "ItacurubidelaCordillera"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "Piribebuy"}, {"Country Code": "PY", "Region Code": "Cordillera", "City Code": "SanBernardino"}, {"Country Code": "EC", "Region Code": "ElOro", "City Code": "Balsas"}, {"Country Code": "EC", "Region Code": "Guayas", "City Code": "La<PERSON>iber<PERSON>"}, {"Country Code": "EC", "Region Code": "Guayas", "City Code": "SantaLucia"}, {"Country Code": "EC", "Region Code": "Orellana", "City Code": "LaJoyadelosSachas"}, {"Country Code": "EC", "Region Code": "Orellana", "City Code": "Loreto"}, {"Country Code": "EC", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Cuenca"}, {"Country Code": "EC", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Atacames"}, {"Country Code": "EC", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "LaConcordia"}, {"Country Code": "EC", "Region Code": "I<PERSON>bur<PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "EC", "Region Code": "ZamoraChinchipe", "City Code": "ElPangui"}, {"Country Code": "EC", "Region Code": "ZamoraChinchipe", "City Code": "Zamora"}, {"Country Code": "AT", "Region Code": "Salzburg", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AT", "Region Code": "Salzburg", "City Code": "Salzburg"}, {"Country Code": "AT", "Region Code": "Salzburg", "City Code": "Tamsweg"}, {"Country Code": "AT", "Region Code": "Salzburg", "City Code": "ZellamSee"}, {"Country Code": "AT", "Region Code": "Vorarlberg", "City Code": "Bregenz"}, {"Country Code": "AT", "Region Code": "Vorarlberg", "City Code": "Dornbirn"}, {"Country Code": "PL", "Region Code": "KujawskoPomorskie", "City Code": "Bydgoszcz"}, {"Country Code": "PE", "Region Code": "La<PERSON>iber<PERSON>", "City Code": "Guadalupe"}, {"Country Code": "PE", "Region Code": "La<PERSON>iber<PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "PE", "Region Code": "Lima", "City Code": "Barranca"}, {"Country Code": "PE", "Region Code": "Lima", "City Code": "Imperial"}, {"Country Code": "PE", "Region Code": "Lima", "City Code": "Lima"}, {"Country Code": "PE", "Region Code": "Lima", "City Code": "SanIsidro"}, {"Country Code": "PE", "Region Code": "<PERSON><PERSON>", "City Code": "Concepcion"}, {"Country Code": "PE", "Region Code": "<PERSON><PERSON>", "City Code": "SanRamon"}, {"Country Code": "PE", "Region Code": "Cusco", "City Code": "SantaAna"}, {"Country Code": "PE", "Region Code": "Ica", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "PE", "Region Code": "Ica", "City Code": "SanJuanBautista"}, {"Country Code": "PE", "Region Code": "Amazonas", "City Code": "ElPorvenir"}, {"Country Code": "PE", "Region Code": "Ayacucho", "City Code": "SanMiguel"}, {"Country Code": "PE", "Region Code": "Loreto", "City Code": "Barranca"}, {"Country Code": "PE", "Region Code": "Loreto", "City Code": "Belen"}, {"Country Code": "PE", "Region Code": "MadredeDios", "City Code": "Iberia"}, {"Country Code": "PE", "Region Code": "Pasco", "City Code": "VillaRica"}, {"Country Code": "PE", "Region Code": "Ucayali", "City Code": "Campoverde"}, {"Country Code": "PE", "Region Code": "Ucayali", "City Code": "ElTambo"}, {"Country Code": "HU", "Region Code": "Pest", "City Code": "<PERSON><PERSON>"}, {"Country Code": "HU", "Region Code": "Tolna", "City Code": "<PERSON><PERSON>"}, {"Country Code": "HU", "Region Code": "Tolna", "City Code": "Zomba"}, {"Country Code": "BG", "Region Code": "Plovdiv", "City Code": "<PERSON>"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "Bansko"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "Belits<PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "Blagoevgrad"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "Hadzhidimovo"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "Kresna"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "Razlog"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Blagoevgrad", "City Code": "Ya<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON>", "City Code": "Breznik"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON>", "City Code": "Radomir"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON>", "City Code": "Tran"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON>", "City Code": "Zemen"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "Belene"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "ChervenBryag"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "DolniDabnik"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "Nikopol"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "Pleven"}, {"Country Code": "BG", "Region Code": "Pleven", "City Code": "<PERSON>rdi<PERSON>"}, {"Country Code": "BG", "Region Code": "Razgrad", "City Code": "Isperih"}, {"Country Code": "BG", "Region Code": "Razgrad", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Razgrad", "City Code": "Loznitsa"}, {"Country Code": "BG", "Region Code": "Razgrad", "City Code": "Razgrad"}, {"Country Code": "BG", "Region Code": "Razgrad", "City Code": "Samuil"}, {"Country Code": "BG", "Region Code": "Ruse", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Ruse", "City Code": "DveMogili"}, {"Country Code": "BG", "Region Code": "Ruse", "City Code": "Ruse"}, {"Country Code": "BG", "Region Code": "Ruse", "City Code": "SlivoPole"}, {"Country Code": "BG", "Region Code": "Ruse", "City Code": "Vetovo"}, {"Country Code": "BG", "Region Code": "Silistra", "City Code": "Alfatar"}, {"Country Code": "BG", "Region Code": "Silistra", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Silistra", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Silistra", "City Code": "Silistra"}, {"Country Code": "BG", "Region Code": "Silistra", "City Code": "Tutrakan"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "Banite"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "Dospat"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "Rudozem"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "Smolyan"}, {"Country Code": "BG", "Region Code": "Smolyan", "City Code": "Zlatograd"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Botevgrad"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Bozhurishte"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "DolnaBanya"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Etropole"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "God<PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Kostinbrod"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Pirdop"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON>rave<PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Slivnitsa"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Svoge"}, {"Country Code": "BG", "Region Code": "Sofia", "City Code": "Zlat<PERSON>a"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "Maglizh"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "Nikolaevo"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "Radnevo"}, {"Country Code": "BG", "Region Code": "StaraZagora", "City Code": "StaraZagora"}, {"Country Code": "BG", "Region Code": "Targovishte", "City Code": "Omurtag"}, {"Country Code": "BG", "Region Code": "Targovishte", "City Code": "Popovo"}, {"Country Code": "BG", "Region Code": "Targovishte", "City Code": "Targovishte"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "Dimitrovgrad"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "Has<PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "Ivaylovgrad"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "Lyubimets"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "MineralniBani"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "Simeonovgrad"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "Svilengrad"}, {"Country Code": "BG", "Region Code": "Has<PERSON><PERSON>", "City Code": "Topolovgrad"}, {"Country Code": "BG", "Region Code": "Shumen", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Shumen", "City Code": "NoviPazar"}, {"Country Code": "BG", "Region Code": "Shumen", "City Code": "Shumen"}, {"Country Code": "BG", "Region Code": "Shumen", "City Code": "Varbitsa"}, {"Country Code": "BG", "Region Code": "Shumen", "City Code": "VelikiPreslav"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "ByalaSlatina"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Hay<PERSON>in"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Knezha"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Krivodol"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Mizia"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Oryahovo"}, {"Country Code": "BG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Dobrich", "City Code": "Balchik"}, {"Country Code": "BG", "Region Code": "Dobrich", "City Code": "Dobrich"}, {"Country Code": "BG", "Region Code": "Dobrich", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Dobrich", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Dobrich", "City Code": "Tervel"}, {"Country Code": "BG", "Region Code": "Kardzhali", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Kardzhali", "City Code": "Chernoochene"}, {"Country Code": "BG", "Region Code": "Kardzhali", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BG", "Region Code": "Kardzhali", "City Code": "Kardzhali"}, {"Country Code": "BG", "Region Code": "Kardzhali", "City Code": "Krumovgrad"}, {"Country Code": "SD", "Region Code": "Khartoum", "City Code": "Khartoum"}, {"Country Code": "SD", "Region Code": "Khartoum", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "SD", "Region Code": "RedSea", "City Code": "PortSudan"}, {"Country Code": "RO", "Region Code": "Teleorman", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NG", "Region Code": "AkwaIbom", "City Code": "IkotEkpene"}, {"Country Code": "NG", "Region Code": "AkwaIbom", "City Code": "North"}, {"Country Code": "NG", "Region Code": "AkwaIbom", "City Code": "U<PERSON>"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "AdoOdo"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "Iwo"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "Osogbo"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "Ganye"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "Maiduguri"}, {"Country Code": "NG", "Region Code": "Enugu", "City Code": "Nsukka"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "Duk<PERSON>"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NG", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "B<PERSON>in<PERSON><PERSON><PERSON>"}, {"Country Code": "NG", "Region Code": "Niger", "City Code": "Kontagora"}, {"Country Code": "NG", "Region Code": "Niger", "City Code": "Lapa<PERSON>"}, {"Country Code": "NL", "Region Code": "Overijssel", "City Code": "Zwolle"}, {"Country Code": "NP", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "NP", "Region Code": "<PERSON><PERSON>", "City Code": "Ilam"}, {"Country Code": "NP", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "KZ", "Region Code": "AqmolaOblysy", "City Code": "Akkol"}, {"Country Code": "KZ", "Region Code": "AqmolaOblysy", "City Code": "Astrakhan"}, {"Country Code": "KZ", "Region Code": "AqmolaOblysy", "City Code": "Atbasar"}, {"Country Code": "KZ", "Region Code": "AqmolaOblysy", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "KZ", "Region Code": "AqmolaOblysy", "City Code": "Esil"}, {"Country Code": "KZ", "Region Code": "AqmolaOblysy", "City Code": "Shortandy"}, {"Country Code": "GH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GH", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "ZM", "Region Code": "Copperbelt", "City Code": "<PERSON><PERSON>"}, {"Country Code": "KE", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FI", "Region Code": "Pirkanmaa", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "FI", "Region Code": "Pirkanmaa", "City Code": "Tampere"}, {"Country Code": "FI", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FI", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "FI", "Region Code": "Uusimaa", "City Code": "Helsinki"}, {"Country Code": "FI", "Region Code": "Uusimaa", "City Code": "Porvoo"}, {"Country Code": "FI", "Region Code": "Uusimaa", "City Code": "Raseborg"}, {"Country Code": "FI", "Region Code": "Kai<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "FI", "Region Code": "Kai<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "FI", "Region Code": "KantaHame", "City Code": "Forssa"}, {"Country Code": "FI", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Nordland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Nordland", "City Code": "Tysnes"}, {"Country Code": "NO", "Region Code": "Nordland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Nordland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "Bokn"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "Haugesund"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "Hjelmeland"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "Sauda"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "Sola"}, {"Country Code": "NO", "Region Code": "Rogaland", "City Code": "Stavanger"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "Elverum"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "Kongsvinger"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "Ringebu"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "Tynset"}, {"Country Code": "NO", "Region Code": "Innlandet", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "Bygland"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "Farsund"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "Gjer<PERSON>"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "Lillesand"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "Lyng<PERSON>"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "Valle"}, {"Country Code": "NO", "Region Code": "<PERSON>gder", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Askvoll"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Austrheim"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "<PERSON>"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Eidfjord"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Etne"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Garden"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "H<PERSON>lestad"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Masfjorden"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Sogndal"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Stryn"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Sveio"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "Ulvik"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NO", "Region Code": "Vestland", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CH", "Region Code": "Fribourg", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CH", "Region Code": "Bern", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CH", "Region Code": "Aargau", "City Code": "Baden"}, {"Country Code": "CH", "Region Code": "Thurgau", "City Code": "Berg"}, {"Country Code": "CH", "Region Code": "BaselLandschaft", "City Code": "Waldenburg"}, {"Country Code": "CH", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CH", "Region Code": "<PERSON>ug", "City Code": "<PERSON>ug"}, {"Country Code": "NZ", "Region Code": "Auckland", "City Code": "Auckland"}, {"Country Code": "NZ", "Region Code": "Auckland", "City Code": "Torbay"}, {"Country Code": "NZ", "Region Code": "BayofPlenty", "City Code": "Rotoru<PERSON>"}, {"Country Code": "NZ", "Region Code": "Canterbury", "City Code": "Lincoln"}, {"Country Code": "NZ", "Region Code": "Canterbury", "City Code": "Oxford"}, {"Country Code": "NZ", "Region Code": "Canterbury", "City Code": "Winchester"}, {"Country Code": "NZ", "Region Code": "<PERSON>", "City Code": "<PERSON>"}, {"Country Code": "NZ", "Region Code": "Northland", "City Code": "Whangarei"}, {"Country Code": "NZ", "Region Code": "Otago", "City Code": "<PERSON><PERSON>"}, {"Country Code": "NZ", "Region Code": "Otago", "City Code": "Paradise"}, {"Country Code": "NZ", "Region Code": "Otago", "City Code": "Weston"}, {"Country Code": "NZ", "Region Code": "WestCoast", "City Code": "<PERSON>"}, {"Country Code": "PT", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Pombal"}, {"Country Code": "PT", "Region Code": "Portalegre", "City Code": "CampoMaior"}, {"Country Code": "PT", "Region Code": "Porto", "City Code": "Amarante"}, {"Country Code": "PT", "Region Code": "Porto", "City Code": "<PERSON>"}, {"Country Code": "PT", "Region Code": "Porto", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "PT", "Region Code": "Porto", "City Code": "Porto"}, {"Country Code": "PT", "Region Code": "Porto", "City Code": "RioTinto"}, {"Country Code": "PT", "Region Code": "Porto", "City Code": "Sousa"}, {"Country Code": "PT", "Region Code": "Porto", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "PT", "Region Code": "VianadoCastelo", "City Code": "Serra"}, {"Country Code": "PT", "Region Code": "Braga", "City Code": "Real"}, {"Country Code": "PT", "Region Code": "Coimbra", "City Code": "Cantanhede"}, {"Country Code": "PT", "Region Code": "Faro", "City Code": "Faro"}, {"Country Code": "PT", "Region Code": "Guarda", "City Code": "Trancoso"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Cheria"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "Tlemcen", "City Code": "Mansoura"}, {"Country Code": "DZ", "Region Code": "Tlemcen", "City Code": "Tlemcen"}, {"Country Code": "DZ", "Region Code": "Tiaret", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "Tiaret", "City Code": "Tiaret"}, {"Country Code": "DZ", "Region Code": "TiziOuzou", "City Code": "TiziOuzou"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "Annaba", "City Code": "Annaba"}, {"Country Code": "DZ", "Region Code": "Constantine", "City Code": "Constantine"}, {"Country Code": "DZ", "Region Code": "Medea", "City Code": "Medea"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "Mascara", "City Code": "Mascara"}, {"Country Code": "DZ", "Region Code": "Laghouat", "City Code": "Laghouat"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON>", "City Code": "EsSenia"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "BordjBouArreridj", "City Code": "BordjBouArreridj"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "ElOued", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "AinTemouchent", "City Code": "AinTemouchent"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DZ", "Region Code": "Batna", "City Code": "Batna"}, {"Country Code": "DZ", "Region Code": "Bejaia", "City Code": "Bejaia"}, {"Country Code": "DZ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "LK", "Region Code": "WesternProvince", "City Code": "Colombo"}, {"Country Code": "TN", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TN", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Sbikha"}, {"Country Code": "TN", "Region Code": "Gafsa", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TN", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "AF", "Region Code": "Kabul", "City Code": "Kabul"}, {"Country Code": "AF", "Region Code": "Kunduz", "City Code": "Kunduz"}, {"Country Code": "AO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Lobito"}, {"Country Code": "AO", "Region Code": "Cabinda", "City Code": "Cabinda"}, {"Country Code": "AO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "AO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Viana"}, {"Country Code": "AO", "Region Code": "<PERSON><PERSON>", "City Code": "Viana"}, {"Country Code": "AZ", "Region Code": "Astara", "City Code": "Astara"}, {"Country Code": "AZ", "Region Code": "Baki", "City Code": "Baku"}, {"Country Code": "AZ", "Region Code": "Quba", "City Code": "Quba"}, {"Country Code": "BS", "Region Code": "NewProvidence", "City Code": "Nassau"}, {"Country Code": "BB", "Region Code": "SaintThomas", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BB", "Region Code": "SaintAndrew", "City Code": "Belle<PERSON>e"}, {"Country Code": "BZ", "Region Code": "Belize", "City Code": "SanPedro"}, {"Country Code": "BJ", "Region Code": "Atlantique", "City Code": "AbomeyCalavi"}, {"Country Code": "BJ", "Region Code": "Atlantique", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BJ", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Parakou"}, {"Country Code": "BJ", "Region Code": "Littoral", "City Code": "Cotonou"}, {"Country Code": "BJ", "Region Code": "<PERSON><PERSON>", "City Code": "Bo<PERSON><PERSON>"}, {"Country Code": "BO", "Region Code": "ElBeni", "City Code": "SanRamon"}, {"Country Code": "BO", "Region Code": "ElBeni", "City Code": "Trinidad"}, {"Country Code": "BO", "Region Code": "Chuquisaca", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BO", "Region Code": "Chuquisaca", "City Code": "<PERSON><PERSON>"}, {"Country Code": "BO", "Region Code": "Chuquisaca", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BO", "Region Code": "LaPaz", "City Code": "LaPaz"}, {"Country Code": "BO", "Region Code": "LaPaz", "City Code": "SanPedro"}, {"Country Code": "BW", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "BN", "Region Code": "<PERSON><PERSON>", "City Code": "KualaBelait"}, {"Country Code": "BI", "Region Code": "Gitega", "City Code": "Gitega"}, {"Country Code": "CF", "Region Code": "HauteKotto", "City Code": "Bria"}, {"Country Code": "CF", "Region Code": "HautMbomou", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CF", "Region Code": "Ouaka", "City Code": "Bambari"}, {"Country Code": "CF", "Region Code": "Ouaka", "City Code": "I<PERSON>"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "Alajuela"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "Desamparados"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "Fortuna"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "Orotina"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "<PERSON><PERSON>"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "SanJuan"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "SanRamon"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "Upala"}, {"Country Code": "CR", "Region Code": "Alajuela", "City Code": "Zarcero"}, {"Country Code": "CR", "Region Code": "Cartago", "City Code": "Cartago"}, {"Country Code": "CR", "Region Code": "Cartago", "City Code": "Concepcion"}, {"Country Code": "CR", "Region Code": "Cartago", "City Code": "Tu<PERSON>alba"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "Bagaces"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "Belen"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "Canas"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "Fortuna"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "LaCruz"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "Liberia"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "Nandayure"}, {"Country Code": "CR", "Region Code": "Guanacaste", "City Code": "Nicoya"}, {"Country Code": "CR", "Region Code": "Heredia", "City Code": "Heredia"}, {"Country Code": "CR", "Region Code": "Heredia", "City Code": "Mercedes"}, {"Country Code": "CR", "Region Code": "Heredia", "City Code": "SanAntonio"}, {"Country Code": "CI", "Region Code": "Abidjan", "City Code": "Abidjan"}, {"Country Code": "CI", "Region Code": "Lacs", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "CU", "Region Code": "SantiagodeCuba", "City Code": "Contramaestre"}, {"Country Code": "TL", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Maliana"}, {"Country Code": "SV", "Region Code": "SantaAna", "City Code": "Chalchuapa"}, {"Country Code": "SV", "Region Code": "SantaAna", "City Code": "ElCongo"}, {"Country Code": "SV", "Region Code": "SantaAna", "City Code": "SantaAna"}, {"Country Code": "SV", "Region Code": "SanMiguel", "City Code": "SanMiguel"}, {"Country Code": "SV", "Region Code": "Sonsonate", "City Code": "Acajutla"}, {"Country Code": "SV", "Region Code": "Sonsonate", "City Code": "Izalco"}, {"Country Code": "SV", "Region Code": "Sonsonate", "City Code": "Nahuizalco"}, {"Country Code": "SV", "Region Code": "Sonsonate", "City Code": "Sonsonate"}, {"Country Code": "SV", "Region Code": "Sonsonate", "City Code": "Sonzacate"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "G<PERSON>zapa"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "Ilopango"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "Mejicanos"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "SanMarcos"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "SanSalvador"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "Soyapango"}, {"Country Code": "SV", "Region Code": "SanSalvador", "City Code": "Tonacatepeque"}, {"Country Code": "GQ", "Region Code": "BiokoNorte", "City Code": "Malabo"}, {"Country Code": "GQ", "Region Code": "CentroSur", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "GQ", "Region Code": "Litoral", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GQ", "Region Code": "<PERSON>le<PERSON>zas", "City Code": "Mongomo"}, {"Country Code": "EE", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Tallinn"}, {"Country Code": "ET", "Region Code": "AddisAbaba", "City Code": "AddisAbaba"}, {"Country Code": "GM", "Region Code": "Banjul", "City Code": "Banjul"}, {"Country Code": "GM", "Region Code": "LowerRiver", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GM", "Region Code": "CentralRiver", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GM", "Region Code": "CentralRiver", "City Code": "<PERSON>"}, {"Country Code": "GE", "Region Code": "<PERSON>mere<PERSON>", "City Code": "Tsqaltubo"}, {"Country Code": "GE", "Region Code": "KvemoK<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "GE", "Region Code": "KvemoK<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GE", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "GT", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Esquipulas"}, {"Country Code": "GT", "Region Code": "Huehuetenango", "City Code": "ElSalvador"}, {"Country Code": "GT", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Livingston"}, {"Country Code": "GT", "Region Code": "Ju<PERSON>pa", "City Code": "ElProgreso"}, {"Country Code": "GT", "Region Code": "Ju<PERSON>pa", "City Code": "<PERSON><PERSON>"}, {"Country Code": "GT", "Region Code": "Quetzaltenango", "City Code": "LaEsperanza"}, {"Country Code": "GT", "Region Code": "SanMarcos", "City Code": "SanMarcos"}, {"Country Code": "GT", "Region Code": "Zacapa", "City Code": "Zacapa"}, {"Country Code": "GN", "Region Code": "Conakry", "City Code": "Conakry"}, {"Country Code": "GY", "Region Code": "DemeraraMahaica", "City Code": "Georgetown"}, {"Country Code": "GY", "Region Code": "PotaroSip<PERSON>", "City Code": "Saint<PERSON><PERSON><PERSON>"}, {"Country Code": "HN", "Region Code": "Comayagua", "City Code": "Comayagua"}, {"Country Code": "HN", "Region Code": "Comayagua", "City Code": "ElPorvenir"}, {"Country Code": "HN", "Region Code": "Comayagua", "City Code": "La<PERSON>iber<PERSON>"}, {"Country Code": "HN", "Region Code": "Comayagua", "City Code": "Siguatepeque"}, {"Country Code": "HN", "Region Code": "Comayagua", "City Code": "VilladeSanAntonio"}, {"Country Code": "HN", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Erandique"}, {"Country Code": "HN", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "HN", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IR", "Region Code": "Zanjan", "City Code": "Zanjan"}, {"Country Code": "IR", "Region Code": "Yazd", "City Code": "Yazd"}, {"Country Code": "IR", "Region Code": "Hormoz<PERSON>", "City Code": "<PERSON>ar<PERSON><PERSON><PERSON>"}, {"Country Code": "IR", "Region Code": "Hormoz<PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "IR", "Region Code": "Tehran", "City Code": "Tehran"}, {"Country Code": "IR", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IR", "Region Code": "Alborz", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IR", "Region Code": "Khuzestan", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "IR", "Region Code": "Kerman", "City Code": "Bam"}, {"Country Code": "IR", "Region Code": "Kerman", "City Code": "Kerman"}, {"Country Code": "IR", "Region Code": "Kerman", "City Code": "<PERSON><PERSON>"}, {"Country Code": "IR", "Region Code": "Kerman", "City Code": "Zarand"}, {"Country Code": "JM", "Region Code": "Westmoreland", "City Code": "Bluefields"}, {"Country Code": "JM", "Region Code": "Westmoreland", "City Code": "Negril"}, {"Country Code": "JM", "Region Code": "Westmoreland", "City Code": "Petersfield"}, {"Country Code": "JM", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "BlackRiver"}, {"Country Code": "JM", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Lacovia"}, {"Country Code": "JM", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Southfield"}, {"Country Code": "JM", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "TreasureBeach"}, {"Country Code": "JM", "Region Code": "Manchester", "City Code": "BrownsTown"}, {"Country Code": "JM", "Region Code": "Manchester", "City Code": "Po<PERSON>"}, {"Country Code": "JM", "Region Code": "Clarendon", "City Code": "Chapelton"}, {"Country Code": "JM", "Region Code": "Clarendon", "City Code": "LionelTown"}, {"Country Code": "JM", "Region Code": "SaintC<PERSON><PERSON>", "City Code": "BogWalk"}, {"Country Code": "JM", "Region Code": "SaintC<PERSON><PERSON>", "City Code": "E<PERSON><PERSON>"}, {"Country Code": "JM", "Region Code": "SaintC<PERSON><PERSON>", "City Code": "Linstead"}, {"Country Code": "JM", "Region Code": "SaintC<PERSON><PERSON>", "City Code": "OldHarbour"}, {"Country Code": "JM", "Region Code": "SaintC<PERSON><PERSON>", "City Code": "OldHarbourBay"}, {"Country Code": "JM", "Region Code": "SaintC<PERSON><PERSON>", "City Code": "Riversdale"}, {"Country Code": "JM", "Region Code": "Hanover", "City Code": "Cascade"}, {"Country Code": "JM", "Region Code": "Hanover", "City Code": "SandyBay"}, {"Country Code": "KI", "Region Code": "GilbertIslands", "City Code": "<PERSON><PERSON>"}, {"Country Code": "KG", "Region Code": "Batken", "City Code": "Batken"}, {"Country Code": "LV", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Kandava"}, {"Country Code": "LV", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Tukums"}, {"Country Code": "LB", "Region Code": "LibanNord", "City Code": "Tripoli"}, {"Country Code": "LB", "Region Code": "LibanNord", "City Code": "Zgharta"}, {"Country Code": "LU", "Region Code": "<PERSON>llen", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON>llen", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON>llen", "City Code": "<PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON>llen", "City Code": "<PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Troisvierges"}, {"Country Code": "LU", "Region Code": "Diekirch", "City Code": "Bettendorf"}, {"Country Code": "LU", "Region Code": "Diekirch", "City Code": "Diekirch"}, {"Country Code": "LU", "Region Code": "Diekirch", "City Code": "Ettelbruck"}, {"Country Code": "LU", "Region Code": "Diekirch", "City Code": "Mertzig"}, {"Country Code": "LU", "Region Code": "Diekirch", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "Echternach", "City Code": "Beaufort"}, {"Country Code": "LU", "Region Code": "Echternach", "City Code": "Consdorf"}, {"Country Code": "LU", "Region Code": "Echternach", "City Code": "Echternach"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "Bettembourg"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "Differdange"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "Dudelange"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "EschsurAlzette"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "<PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "Leudelang<PERSON>"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "Mondercange"}, {"Country Code": "LU", "Region Code": "EschsurAlzette", "City Code": "Schifflang<PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Betzdorf"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Mertert"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON>", "City Code": "Bissen"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "RedangesurAttert"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "MondorflesBains"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "Vianden", "City Code": "<PERSON><PERSON>"}, {"Country Code": "LU", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "ML", "Region Code": "Koulikoro", "City Code": "<PERSON><PERSON>"}, {"Country Code": "ML", "Region Code": "Koulikoro", "City Code": "Koulikoro"}, {"Country Code": "ML", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Kouti<PERSON>"}, {"Country Code": "ML", "Region Code": "Ba<PERSON><PERSON>", "City Code": "Ba<PERSON><PERSON>"}, {"Country Code": "MR", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Nouadhibou"}, {"Country Code": "MU", "Region Code": "BlackRiver", "City Code": "Albion"}, {"Country Code": "MU", "Region Code": "Flacq", "City Code": "BelAir"}, {"Country Code": "MU", "Region Code": "PortLouis", "City Code": "PortLouis"}, {"Country Code": "MA", "Region Code": "TangerTetouanAlHoceima", "City Code": "Tetouan"}, {"Country Code": "MA", "Region Code": "CasablancaSettat", "City Code": "Casablanca"}, {"Country Code": "MA", "Region Code": "CasablancaSettat", "City Code": "Mohammedia"}, {"Country Code": "MA", "Region Code": "CasablancaSettat", "City Code": "Nouaceur"}, {"Country Code": "MA", "Region Code": "SoussMassa", "City Code": "SidiIfni"}, {"Country Code": "MZ", "Region Code": "Tete", "City Code": "Tete"}, {"Country Code": "NI", "Region Code": "Managua", "City Code": "ElCrucero"}, {"Country Code": "NE", "Region Code": "Niamey", "City Code": "Niamey"}, {"Country Code": "PA", "Region Code": "LosSantos", "City Code": "BellaVista"}, {"Country Code": "PA", "Region Code": "LosSantos", "City Code": "LosSantos"}, {"Country Code": "PG", "Region Code": "Morobe", "City Code": "Lae"}, {"Country Code": "PG", "Region Code": "Madang", "City Code": "Madang"}, {"Country Code": "LC", "Region Code": "Choiseul", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "SN", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "SN", "Region Code": "<PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "SN", "Region Code": "Kaolack", "City Code": "Kaolack"}, {"Country Code": "SN", "Region Code": "Matam", "City Code": "Matam"}, {"Country Code": "SN", "Region Code": "Tambacounda", "City Code": "Tambacounda"}, {"Country Code": "SN", "Region Code": "Zig<PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "SC", "Region Code": "Cascade", "City Code": "Cascade"}, {"Country Code": "SC", "Region Code": "EnglishRiver", "City Code": "Victoria"}, {"Country Code": "SC", "Region Code": "BaieSainte<PERSON><PERSON>", "City Code": "CotedOr"}, {"Country Code": "SI", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "SI", "Region Code": "Radovljica", "City Code": "Radovljica"}, {"Country Code": "SI", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "SI", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "SI", "Region Code": "Trbovlje", "City Code": "Trbovlje"}, {"Country Code": "SI", "Region Code": "Trebnje", "City Code": "Trebnje"}, {"Country Code": "SI", "Region Code": "Vodice", "City Code": "Vodice"}, {"Country Code": "SI", "Region Code": "Komenda", "City Code": "Komenda"}, {"Country Code": "SI", "Region Code": "Oplotnica", "City Code": "Oplotnica"}, {"Country Code": "SI", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "SI", "Region Code": "GornjaRadgona", "City Code": "GornjaRadgona"}, {"Country Code": "SI", "Region Code": "Hrastnik", "City Code": "Hrastnik"}, {"Country Code": "SI", "Region Code": "Kranj", "City Code": "Kranj"}, {"Country Code": "SI", "Region Code": "Kungota", "City Code": "Pesnica"}, {"Country Code": "SI", "Region Code": "Ljubljana", "City Code": "Ljubljana"}, {"Country Code": "SI", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "SI", "Region Code": "Logatec", "City Code": "Logatec"}, {"Country Code": "SI", "Region Code": "Maribor", "City Code": "Maribor"}, {"Country Code": "SI", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "B<PERSON><PERSON>"}, {"Country Code": "SI", "Region Code": "Brezovica", "City Code": "Brezovica"}, {"Country Code": "SI", "Region Code": "NovoMesto", "City Code": "NovoMesto"}, {"Country Code": "SI", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "SO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Baki"}, {"Country Code": "SS", "Region Code": "CentralEquatoria", "City Code": "Juba"}, {"Country Code": "SY", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Damascus"}, {"Country Code": "TJ", "Region Code": "Sughd", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TG", "Region Code": "Centrale", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "TG", "Region Code": "Maritime", "City Code": "<PERSON><PERSON>"}, {"Country Code": "TO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "UY", "Region Code": "Artigas", "City Code": "BaltasarBrum"}, {"Country Code": "UY", "Region Code": "Artigas", "City Code": "BellaUnion"}, {"Country Code": "UY", "Region Code": "Artigas", "City Code": "LasPiedras"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "AguasCorrientes"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "Atlantida"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "Canelones"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "ColoniaN<PERSON>lich"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "EmpalmeOlmos"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "LaFloresta"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "LaPaz"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "LasPiedras"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "LosCerrillos"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "Montes"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "Pando"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "SanBautista"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "SanRamon"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "SantaLucia"}, {"Country Code": "UY", "Region Code": "Canelones", "City Code": "Sauce"}, {"Country Code": "UY", "Region Code": "CerroLargo", "City Code": "RioBranco"}, {"Country Code": "UY", "Region Code": "Colonia", "City Code": "Cardona"}, {"Country Code": "UY", "Region Code": "Colonia", "City Code": "Carmel<PERSON>"}, {"Country Code": "UY", "Region Code": "Colonia", "City Code": "NuevaHelvecia"}, {"Country Code": "UY", "Region Code": "Colonia", "City Code": "NuevaPalmira"}, {"Country Code": "UY", "Region Code": "Colonia", "City Code": "OmbuesdeLavalle"}, {"Country Code": "UY", "Region Code": "Colonia", "City Code": "Rosario"}, {"Country Code": "UY", "Region Code": "Colonia", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "UY", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Minas"}, {"Country Code": "UY", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "SolisdeMataojo"}, {"Country Code": "UY", "Region Code": "Maldonado", "City Code": "Maldonado"}, {"Country Code": "UY", "Region Code": "Maldonado", "City Code": "PandeAzucar"}, {"Country Code": "UY", "Region Code": "Maldonado", "City Code": "Piriapolis"}, {"Country Code": "UY", "Region Code": "Maldonado", "City Code": "PuntadelEste"}, {"Country Code": "UY", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON>"}, {"Country Code": "UY", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Mercedes"}, {"Country Code": "UY", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Palmitas"}, {"Country Code": "VE", "Region Code": "Aragua", "City Code": "LaVictoria"}, {"Country Code": "VE", "Region Code": "Aragua", "City Code": "SanCasimiro"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON>", "City Code": "SantaBarbara"}, {"Country Code": "VE", "Region Code": "Carabobo", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "VE", "Region Code": "<PERSON>", "City Code": "<PERSON>"}, {"Country Code": "VE", "Region Code": "<PERSON>", "City Code": "SantaTeresa"}, {"Country Code": "VE", "Region Code": "NuevaEsparta", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Guadalajara"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "Rosario"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "SantaAna"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "SantaRita"}, {"Country Code": "VE", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON>"}, {"Country Code": "YE", "Region Code": "AlHudaydah", "City Code": "AlMansuriyah"}, {"Country Code": "YE", "Region Code": "AlHudaydah", "City Code": "<PERSON><PERSON>"}, {"Country Code": "ZW", "Region Code": "Bulawayo", "City Code": "Bulawayo"}, {"Country Code": "ZW", "Region Code": "Harare", "City Code": "Harare"}, {"Country Code": "ZW", "Region Code": "MashonalandWest", "City Code": "<PERSON>"}, {"Country Code": "DO", "Region Code": "Independencia", "City Code": "LaDescubierta"}, {"Country Code": "DO", "Region Code": "Independencia", "City Code": "<PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "LaAltagracia", "City Code": "SanRafaeldel<PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "LaRomana", "City Code": "LaRomana"}, {"Country Code": "DO", "Region Code": "LaVega", "City Code": "Constanza"}, {"Country Code": "DO", "Region Code": "LaVega", "City Code": "Jarabacoa"}, {"Country Code": "DO", "Region Code": "LaVega", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "LaVega", "City Code": "VillaTapia"}, {"Country Code": "DO", "Region Code": "MonteCristi", "City Code": "LasMatasdeSantaCruz"}, {"Country Code": "DO", "Region Code": "MonteCristi", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "Peravia", "City Code": "Niza<PERSON>"}, {"Country Code": "DO", "Region Code": "PuertoPlata", "City Code": "Guananico"}, {"Country Code": "DO", "Region Code": "PuertoPlata", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "PuertoPlata", "City Code": "PuertoPlata"}, {"Country Code": "DO", "Region Code": "PuertoPlata", "City Code": "VillaIsabela"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City Code": "Tenares"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "<PERSON><PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "LasCharcas"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "PadreLasCasas"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON><PERSON>", "City Code": "PuebloViejo"}, {"Country Code": "DO", "Region Code": "Santiago", "City Code": "SantiagodelosCaballeros"}, {"Country Code": "DO", "Region Code": "Santiago", "City Code": "Tamboril"}, {"Country Code": "DO", "Region Code": "Valverde", "City Code": "Esperanza"}, {"Country Code": "DO", "Region Code": "Valverde", "City Code": "<PERSON>"}, {"Country Code": "DO", "Region Code": "MontePlata", "City Code": "Bayaguana"}, {"Country Code": "DO", "Region Code": "MontePlata", "City Code": "MontePlata"}, {"Country Code": "DO", "Region Code": "Hat<PERSON><PERSON><PERSON>or", "City Code": "ElValle"}, {"Country Code": "DO", "Region Code": "Hat<PERSON><PERSON><PERSON>or", "City Code": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "Barahona", "City Code": "Cabral"}, {"Country Code": "DO", "Region Code": "Barahona", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON>", "City Code": "Pimentel"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON>", "City Code": "VillaRiva"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "JamaoalNorte"}, {"Country Code": "DO", "Region Code": "<PERSON><PERSON><PERSON><PERSON>", "City Code": "Moca"}, {"Country Code": "BA", "Region Code": "RepublikaSrpska", "City Code": "<PERSON><PERSON><PERSON><PERSON>"}, {"Country Code": "BA", "Region Code": "RepublikaSrpska", "City Code": "Bijeljina"}, {"Country Code": "BA", "Region Code": "RepublikaSrpska", "City Code": "Doboj"}]