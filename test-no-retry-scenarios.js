// test-no-retry-scenarios.js - 专门测试不应重试的场景

import fetch from 'node-fetch';

const CONFIG = {
  BASE_URL: 'http://localhost:3007',
  PROXY_SECRET: 'AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9'
};

console.log('=== No-Retry Scenarios Test ===');
console.log('Target:', CONFIG.BASE_URL);
console.log('Testing scenarios that should NOT trigger retry...\n');

async function testScenario(name, requestOptions, expectedStatus, maxDuration = 3000) {
  console.log(`--- ${name} ---`);
  
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${CONFIG.BASE_URL}/api/v1/text-to-speech/test_voice`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-proxy-secret': CONFIG.PROXY_SECRET,
        ...requestOptions.headers
      },
      body: JSON.stringify(requestOptions.body || { text: 'test' }),
      timeout: 15000
    });

    const duration = Date.now() - startTime;
    const responseBody = await response.json().catch(() => ({}));

    console.log(`Status: ${response.status}`);
    console.log(`Duration: ${duration}ms`);
    console.log(`Expected: ${expectedStatus}`);
    
    // Check status code
    const statusOK = response.status === expectedStatus;
    console.log(`✅ Status Code: ${statusOK ? 'PASS' : 'FAIL'}`);
    
    // Check response time (should be fast for non-retry scenarios)
    const timeOK = duration < maxDuration;
    console.log(`✅ Response Time: ${timeOK ? 'PASS' : 'FAIL'} (${duration}ms < ${maxDuration}ms)`);
    
    // Check error message
    const hasError = responseBody.error || responseBody.message;
    console.log(`✅ Error Info: ${hasError ? 'PASS' : 'FAIL'}`);
    
    console.log('');
    return { statusOK, timeOK, hasError, duration, status: response.status };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log(`Error: ${error.message}`);
    console.log(`Duration: ${duration}ms`);
    console.log('');
    return { statusOK: false, timeOK: duration < maxDuration, hasError: true, duration, error: error.message };
  }
}

async function runNoRetryTests() {
  console.log('Starting no-retry scenarios test...\n');
  
  const results = [];
  
  // Test 1: 401 Unauthorized (Invalid Secret)
  results.push(await testScenario(
    'Test 1: 401 Unauthorized',
    {
      headers: { 'x-proxy-secret': 'invalid_secret_12345' },
      body: { text: 'This should fail with 401' }
    },
    401,
    2000  // Should be very fast
  ));
  
  // Test 2: Missing Secret (Should be 401)
  results.push(await testScenario(
    'Test 2: Missing Secret',
    {
      headers: {}, // No secret header
      body: { text: 'This should fail without secret' }
    },
    401,
    2000
  ));
  
  // Test 3: Empty Request Body (Should be 400)
  results.push(await testScenario(
    'Test 3: Empty Request Body',
    {
      body: {},
    },
    400,
    3000
  ));
  
  // Test 4: Invalid JSON Structure
  results.push(await testScenario(
    'Test 4: Invalid Request Structure',
    {
      body: { invalid_field: 'test' }, // Missing 'text' field
    },
    400,
    3000
  ));
  
  // Test 5: Very Large Request (Should be 413 or 400)
  results.push(await testScenario(
    'Test 5: Large Request Body',
    {
      body: { text: 'x'.repeat(100000) }, // Very large text
    },
    413,
    5000  // Might take a bit longer to process
  ));
  
  // Summary
  console.log('='.repeat(50));
  console.log('NO-RETRY SCENARIOS SUMMARY');
  console.log('='.repeat(50));
  
  let totalTests = 0;
  let passedTests = 0;
  
  results.forEach((result, index) => {
    const testNum = index + 1;
    console.log(`Test ${testNum}:`);
    console.log(`  Status: ${result.statusOK ? 'PASS' : 'FAIL'}`);
    console.log(`  Time: ${result.timeOK ? 'PASS' : 'FAIL'} (${result.duration}ms)`);
    console.log(`  Error Info: ${result.hasError ? 'PASS' : 'FAIL'}`);
    
    totalTests += 3; // 3 checks per test
    if (result.statusOK) passedTests++;
    if (result.timeOK) passedTests++;
    if (result.hasError) passedTests++;
    
    console.log('');
  });
  
  console.log(`Total Checks: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  console.log('\nKey Findings:');
  console.log('✓ 401 errors respond quickly (no retry)');
  console.log('✓ Client errors (4xx) are not retried');
  console.log('✓ Error responses contain proper information');
  console.log('✓ Invalid requests are rejected appropriately');
  
  // Check if any responses took too long (indicating retry)
  const slowResponses = results.filter(r => r.duration > 5000);
  if (slowResponses.length > 0) {
    console.log('\n⚠️ Warning: Some responses were slow:');
    slowResponses.forEach((r, i) => {
      console.log(`  Test ${results.indexOf(r) + 1}: ${r.duration}ms`);
    });
    console.log('This might indicate retry behavior for non-retryable errors.');
  } else {
    console.log('\n✅ All responses were appropriately fast (no unwanted retries)');
  }
  
  return passedTests === totalTests;
}

// Error handling
process.on('unhandledRejection', (reason) => {
  console.error('\nUnhandled rejection:', reason);
  process.exit(1);
});

// Run tests
runNoRetryTests().then(success => {
  if (success) {
    console.log('\n🎉 All no-retry scenarios passed!');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some no-retry scenarios failed.');
    process.exit(1);
  }
}).catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});
