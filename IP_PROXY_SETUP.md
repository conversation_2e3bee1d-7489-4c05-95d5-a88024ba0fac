# 🌐 IP代理配置指南

## 📋 概述

项目专门为动态IP代理服务优化，提供高性能、简化配置的代理解决方案。

## ✅ 配置特点

1. **专用IP代理**：专门为IP代理服务优化
2. **环境变量配置**：支持通过环境变量灵活配置代理信息
3. **简化SSL配置**：IP代理使用简化的SSL配置，性能更优
4. **详细日志**：提供完整的代理状态日志

## 🔧 配置步骤

### 1. 环境变量配置

#### 方法1：使用.env文件（推荐开发环境）

```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

在`.env`文件中设置您的代理信息：
```bash
PROXY_IP=您的代理IP
PROXY_PORT=您的代理端口
PROXY_USERNAME=您的用户名
PROXY_PASSWORD=您的密码
```

#### 方法2：直接设置环境变量（推荐生产环境）

```bash
export PROXY_IP="您的代理IP"
export PROXY_PORT="您的代理端口"
export PROXY_USERNAME="您的用户名"
export PROXY_PASSWORD="您的密码"
```

#### 方法3：PM2配置文件（推荐生产部署）

编辑`ecosystem.config.cjs`文件：
```javascript
env: {
  NODE_ENV: 'production',
  PORT: 3002,
  PROXY_SECRET: 'your_secret',
  PROXY_IP: '您的代理IP',
  PROXY_PORT: '您的代理端口',
  PROXY_USERNAME: '您的用户名',
  PROXY_PASSWORD: '您的密码'
}
```

### 2. 启动服务

```bash
# 开发环境
npm run dev

# 生产环境
pm2 start ecosystem.config.cjs
```

## 📊 配置验证

### 启动日志检查

正确配置后，启动时会看到以下日志：

**使用IP代理时**：
```
[PROXY] ✅ 检测到IP代理配置
[PROXY] 🌐 代理服务器: *************:8080
[PROXY] 👤 用户名: your_username
[PROXY] ✅ 当前使用: IP代理服务
[PROXY] 📡 代理地址: *************:8080
```

**使用备用代理时**：
```
[PROXY] ⚠️ IP代理配置不完整，检查备用方案...
[PROXY] 🔄 使用Bright Data备用代理配置
[PROXY] ⚠️ 当前使用: Bright Data备用代理
[PROXY] 💡 建议配置IP代理环境变量
```

### API调用日志检查

请求ElevenLabs API时的日志：

**IP代理**：
```
[PROXY] 🌐 Using IP proxy *************:8080 for ElevenLabs API
[PROXY] 📡 Proxy URL: http://your_username:***@*************:8080
```

**备用代理**：
```
[PROXY] 🌐 Using Bright Data backup proxy for ElevenLabs API
[PROXY] 📡 Proxy URL: http://brd-customer-***:***@brd.superproxy.io:33335
```

## 🔒 安全建议

1. **密码保护**：
   - 使用强密码
   - 不要在代码中硬编码密码
   - 生产环境使用环境变量

2. **访问控制**：
   - 设置强的PROXY_SECRET
   - 定期更换密钥
   - 监控访问日志

3. **网络安全**：
   - 确保代理服务器的安全性
   - 使用HTTPS传输敏感数据
   - 定期检查代理连接状态

## 🚨 故障排除

### 常见问题

1. **代理连接失败**：
```
[PROXY] 🔌 Possible proxy connection issue - check *************:8080
```
**解决方案**：
- 检查代理IP和端口是否正确
- 验证用户名和密码
- 确认代理服务器状态

2. **配置不完整**：
```
[PROXY] ❌ 无可用代理配置
[PROXY] 📋 请设置以下环境变量:
```
**解决方案**：
- 按照上述步骤配置环境变量
- 检查变量名是否正确
- 确认变量值不为空

3. **SSL证书问题**：
```
[PROXY] ❌ 代理配置失败: SSL Error
```
**解决方案**：
- IP代理通常不需要特殊SSL配置
- 确保NODE_TLS_REJECT_UNAUTHORIZED=0已设置
- 检查代理服务器的SSL支持

## 📈 性能优化

1. **连接超时**：默认30秒，可根据网络情况调整
2. **请求超时**：默认180秒，适合大文件传输
3. **SSL优化**：IP代理使用简化SSL配置，性能更优

## 🔄 备用方案

如果IP代理出现问题，系统会自动切换到Bright Data备用代理：

1. **自动检测**：系统会检测IP代理配置完整性
2. **智能切换**：配置不完整时自动使用备用方案
3. **日志提示**：清晰显示当前使用的代理类型
4. **无缝切换**：不影响现有功能和API调用

## 📞 技术支持

如果遇到配置问题：

1. 检查启动日志中的代理配置信息
2. 验证环境变量设置
3. 测试代理服务器连接性
4. 查看详细的错误日志

配置完成后，所有现有功能将保持不变，只是代理服务器发生了切换。
